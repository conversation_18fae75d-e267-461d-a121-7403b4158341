# Kafka Order Service 部署指南

## 🚀 部署方式

### 1. 本地开发环境

#### 前置条件
- Java 8+
- Maven 3.6+
- Kafka 3.8.1+ (可使用Docker)

#### 步骤
```bash
# 1. 启动Kafka (使用已安装的Kafka)
cd /home/<USER>/code/ai/kafka_example
./start-kafka.sh

# 2. 切换到项目目录
cd /home/<USER>/code/ai/kafka_product_consume

# 3. 编译项目
mvn clean package -DskipTests

# 4. 运行应用
java -jar target/kafka-order-service-1.0.0.jar
```

### 2. Docker单容器部署

```bash
# 构建镜像
docker build -t kafka-order-service:1.0.0 .

# 运行容器
docker run -d \
  --name kafka-order-service \
  -p 8080:8080 \
  -e KAFKA_BOOTSTRAP_SERVERS=host.docker.internal:9092 \
  -e SPRING_PROFILES_ACTIVE=dev \
  kafka-order-service:1.0.0
```

### 3. Docker Compose部署

```bash
# 启动完整环境
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f kafka-order-service
```

### 4. 生产环境部署

#### 使用Docker Swarm
```bash
# 创建Docker Swarm服务
docker service create \
  --name kafka-order-service \
  --replicas 3 \
  --publish 8080:8080 \
  --env SPRING_PROFILES_ACTIVE=prod \
  --env KAFKA_BOOTSTRAP_SERVERS=kafka1:9092,kafka2:9092,kafka3:9092 \
  --env JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC" \
  kafka-order-service:1.0.0
```

#### 使用Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-order-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: kafka-order-service
  template:
    metadata:
      labels:
        app: kafka-order-service
    spec:
      containers:
      - name: kafka-order-service
        image: kafka-order-service:1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka1:9092,kafka2:9092,kafka3:9092"
        - name: JAVA_OPTS
          value: "-Xms2g -Xmx4g -XX:+UseG1GC"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
```

## ⚙️ 环境配置

### 开发环境
```yaml
spring:
  profiles:
    active: dev
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      group-id: order-service-dev-group
    listener:
      concurrency: 1
```

### 测试环境
```yaml
spring:
  profiles:
    active: test
  kafka:
    bootstrap-servers: kafka-test:9092
    consumer:
      group-id: order-service-test-group
    listener:
      concurrency: 2
```

### 生产环境
```yaml
spring:
  profiles:
    active: prod
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS}
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP_ID}
    listener:
      concurrency: ${KAFKA_LISTENER_CONCURRENCY:10}
```

## 🔧 性能调优

### JVM参数优化
```bash
# 生产环境推荐JVM参数
JAVA_OPTS="-Xms2g -Xmx4g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=/var/log/kafka-order-service/ \
  -XX:+UseStringDeduplication \
  -Djava.security.egd=file:/dev/./urandom"
```

### Kafka生产者优化
```yaml
spring:
  kafka:
    producer:
      batch-size: 65536          # 64KB批处理
      linger-ms: 5               # 5ms延迟
      buffer-memory: 134217728   # 128MB缓冲区
      compression-type: lz4      # LZ4压缩
      acks: all                  # 等待所有副本确认
      retries: 5                 # 重试5次
```

### Kafka消费者优化
```yaml
spring:
  kafka:
    consumer:
      max-poll-records: 1000     # 每次拉取1000条消息
      max-poll-interval-ms: 600000  # 10分钟轮询间隔
      session-timeout-ms: 45000     # 45秒会话超时
      fetch-min-size: 4096          # 最小拉取4KB
    listener:
      concurrency: 10            # 10个并发消费者
```

## 📊 监控配置

### Prometheus配置
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'kafka-order-service'
    static_configs:
      - targets: ['kafka-order-service:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s
```

### Grafana仪表板
- JVM监控：内存、GC、线程
- Kafka监控：生产者、消费者指标
- 应用监控：订单处理指标
- 系统监控：CPU、内存、网络

## 🔒 安全配置

### Kafka SSL配置
```yaml
spring:
  kafka:
    security:
      protocol: SSL
    ssl:
      trust-store-location: /path/to/truststore.jks
      trust-store-password: ${KAFKA_SSL_TRUSTSTORE_PASSWORD}
      key-store-location: /path/to/keystore.jks
      key-store-password: ${KAFKA_SSL_KEYSTORE_PASSWORD}
```

### SASL配置
```yaml
spring:
  kafka:
    security:
      protocol: SASL_SSL
    properties:
      sasl.mechanism: PLAIN
      sasl.jaas.config: |
        org.apache.kafka.common.security.plain.PlainLoginModule required
        username="${KAFKA_USERNAME}"
        password="${KAFKA_PASSWORD}";
```

## 🚨 故障排除

### 常见问题

1. **应用启动失败**
   - 检查Java版本
   - 验证Kafka连接
   - 查看启动日志

2. **Kafka连接超时**
   - 检查网络连接
   - 验证Kafka服务状态
   - 检查防火墙设置

3. **内存不足**
   - 调整JVM堆内存
   - 检查内存泄漏
   - 优化GC参数

4. **消息处理延迟**
   - 增加消费者并发数
   - 优化批处理参数
   - 检查业务逻辑性能

### 日志分析
```bash
# 查看应用日志
tail -f logs/kafka-order-service.log | grep ERROR

# 查看GC日志
tail -f gc.log

# 查看Kafka日志
tail -f logs/kafka-order-service-kafka.log
```

## 📈 扩容策略

### 水平扩容
1. 增加应用实例数量
2. 调整Kafka分区数量
3. 增加消费者并发数

### 垂直扩容
1. 增加CPU和内存资源
2. 优化JVM参数
3. 调整线程池配置

## 🔄 滚动更新

### Docker Swarm
```bash
docker service update \
  --image kafka-order-service:1.1.0 \
  --update-parallelism 1 \
  --update-delay 30s \
  kafka-order-service
```

### Kubernetes
```bash
kubectl set image deployment/kafka-order-service \
  kafka-order-service=kafka-order-service:1.1.0
```

## 📋 检查清单

### 部署前检查
- [ ] Kafka集群状态正常
- [ ] 主题已创建
- [ ] 网络连通性测试
- [ ] 配置文件验证
- [ ] 资源配额检查

### 部署后验证
- [ ] 应用健康检查通过
- [ ] Kafka连接正常
- [ ] 监控指标正常
- [ ] 日志输出正常
- [ ] API接口测试通过
