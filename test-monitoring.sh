#!/bin/bash

# 测试监控栈脚本

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🔍 测试监控栈状态${NC}"
echo "================================"

# 1. 检查Docker容器状态
echo -e "${GREEN}📦 检查Docker容器状态...${NC}"
sudo docker-compose ps

echo ""

# 2. 检查端口监听
echo -e "${GREEN}🔌 检查端口监听状态...${NC}"
echo "Prometheus (9090): $(sudo netstat -tlnp | grep :9090 | wc -l) 个监听"
echo "Grafana (3000): $(sudo netstat -tlnp | grep :3000 | wc -l) 个监听"
echo "应用 (8080): $(sudo netstat -tlnp | grep :8080 | wc -l) 个监听"

echo ""

# 3. 测试服务响应
echo -e "${GREEN}🌐 测试服务响应...${NC}"

# 测试应用健康检查
if curl -s -f http://localhost:8080/actuator/health > /dev/null; then
    echo "✅ 应用服务: 正常"
else
    echo "❌ 应用服务: 异常"
fi

# 测试Prometheus
if curl -s -f http://localhost:9090/-/healthy > /dev/null; then
    echo "✅ Prometheus: 正常"
else
    echo "❌ Prometheus: 异常"
fi

# 测试Grafana
if curl -s -f http://localhost:3000/api/health > /dev/null; then
    echo "✅ Grafana: 正常"
else
    echo "❌ Grafana: 异常"
fi

echo ""

# 4. 检查Prometheus目标状态
echo -e "${GREEN}🎯 检查Prometheus目标状态...${NC}"
targets_response=$(curl -s http://localhost:9090/api/v1/targets 2>/dev/null || echo "无法连接")
if [[ "$targets_response" != "无法连接" ]]; then
    echo "Prometheus API响应正常"
    # 尝试解析目标状态
    if echo "$targets_response" | grep -q '"health":"up"'; then
        echo "✅ 有目标处于UP状态"
    elif echo "$targets_response" | grep -q '"health":"down"'; then
        echo "⚠️  有目标处于DOWN状态"
    else
        echo "❓ 目标状态未知"
    fi
else
    echo "❌ 无法连接到Prometheus API"
fi

echo ""

# 5. 测试指标查询
echo -e "${GREEN}📊 测试指标查询...${NC}"
metrics_response=$(curl -s http://localhost:8080/actuator/prometheus 2>/dev/null || echo "无法连接")
if [[ "$metrics_response" != "无法连接" ]]; then
    kafka_metrics=$(echo "$metrics_response" | grep -c "kafka_order" || echo "0")
    echo "✅ 应用指标可访问，找到 $kafka_metrics 个Kafka相关指标"
else
    echo "❌ 无法访问应用指标"
fi

echo ""

# 6. 显示访问地址
echo -e "${BLUE}🔗 监控访问地址:${NC}"
echo "• 应用健康检查: http://localhost:8080/actuator/health"
echo "• 应用指标: http://localhost:8080/actuator/prometheus"
echo "• Prometheus: http://localhost:9090"
echo "• Grafana: http://localhost:3000 (admin/admin)"

echo ""

# 7. 显示一些关键指标
echo -e "${GREEN}📈 当前关键指标:${NC}"
if [[ "$metrics_response" != "无法连接" ]]; then
    sent_total=$(echo "$metrics_response" | grep "kafka_order_sent_total{" | tail -1 | awk '{print $2}' || echo "0")
    received_total=$(echo "$metrics_response" | grep "kafka_order_received_total{" | tail -1 | awk '{print $2}' || echo "0")
    echo "• 发送订单总数: $sent_total"
    echo "• 接收订单总数: $received_total"
fi

echo ""
echo -e "${YELLOW}💡 提示:${NC}"
echo "• 如果在Windows中访问，请使用WSL的IP地址"
echo "• WSL IP: $(hostname -I | awk '{print $1}')"
echo "• 或者在Windows中访问: http://$(hostname -I | awk '{print $1}'):9090"
