# Kafka 服务端安装和使用指南

## 📦 安装信息

- **Kafka 版本**: 3.8.1
- **运行模式**: KRaft (无需 ZooKeeper)
- **Java 版本**: OpenJDK 1.8.0_452
- **安装路径**: `./kafka/`

## 🚀 快速开始

### 1. 启动 Kafka 服务器
```bash
./start-kafka.sh
```

### 2. 停止 Kafka 服务器
```bash
./stop-kafka.sh
```

### 3. 运行功能测试
```bash
./kafka-test.sh
```

## 🌐 连接信息

- **客户端连接地址**: `localhost:9092`
- **控制器地址**: `localhost:9093`
- **日志文件**: `kafka.log`

## 📋 常用命令

### 主题管理
```bash
# 创建主题
./kafka/bin/kafka-topics.sh --create --topic my-topic --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1

# 列出所有主题
./kafka/bin/kafka-topics.sh --list --bootstrap-server localhost:9092

# 查看主题详情
./kafka/bin/kafka-topics.sh --describe --topic my-topic --bootstrap-server localhost:9092

# 删除主题
./kafka/bin/kafka-topics.sh --delete --topic my-topic --bootstrap-server localhost:9092
```

### 生产者和消费者测试
```bash
# 启动控制台生产者
./kafka/bin/kafka-console-producer.sh --topic my-topic --bootstrap-server localhost:9092

# 启动控制台消费者（从最新消息开始）
./kafka/bin/kafka-console-consumer.sh --topic my-topic --bootstrap-server localhost:9092

# 启动控制台消费者（从头开始）
./kafka/bin/kafka-console-consumer.sh --topic my-topic --from-beginning --bootstrap-server localhost:9092
```

## 🔧 配置文件

- **服务器配置**: `./kafka/config/kraft/server.properties`
- **生产者配置**: `./kafka/config/producer.properties`
- **消费者配置**: `./kafka/config/consumer.properties`

## 📊 监控和管理

### 查看 Kafka 进程状态
```bash
ps aux | grep kafka
```

### 查看日志
```bash
tail -f kafka.log
```

### 检查端口占用
```bash
netstat -tlnp | grep :9092
netstat -tlnp | grep :9093
```

## 🛠️ 故障排除

### 1. 启动失败
- 检查 Java 是否正确安装: `java -version`
- 查看日志文件: `cat kafka.log`
- 确保端口 9092 和 9093 没有被占用

### 2. 连接问题
- 确认 Kafka 服务器正在运行: `pgrep -f kafka`
- 检查防火墙设置
- 验证网络连接: `telnet localhost 9092`

### 3. 性能问题
- 调整 JVM 堆内存设置
- 检查磁盘空间
- 监控系统资源使用情况

## 📚 更多资源

- [Apache Kafka 官方文档](https://kafka.apache.org/documentation/)
- [Kafka 快速入门指南](https://kafka.apache.org/quickstart)
- [KRaft 模式说明](https://kafka.apache.org/documentation/#kraft)

## 🎯 下一步

1. 学习 Kafka 的基本概念（主题、分区、副本等）
2. 编写 Java/Python 客户端应用程序
3. 配置集群模式
4. 设置监控和告警
5. 学习 Kafka Streams 和 Kafka Connect
