global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Kafka Order Service
  - job_name: 'kafka-order-service'
    static_configs:
      - targets: ['kafka-order-service:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s
    scrape_timeout: 10s

  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Kafka JMX监控 (如果启用)
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9101']
    scrape_interval: 30s
