# Kafka Order Service - 高性能订单处理系统

基于Spring Boot 2.4和Apache Kafka构建的高性能、生产就绪的订单处理系统。

## 🚀 项目特性

- **高性能**: 优化的Kafka生产者和消费者配置，支持高吞吐量消息处理
- **生产就绪**: 完整的监控、日志、健康检查和错误处理机制
- **容器化**: Docker和Docker Compose支持，便于部署和扩展
- **订单处理**: 完整的订单生命周期管理，包含状态转换和业务逻辑
- **幂等性**: 消息去重和幂等性处理，确保数据一致性
- **可观测性**: Prometheus监控指标和结构化日志

## 📋 技术栈

- **Spring Boot**: 2.4.13
- **Spring Kafka**: 2.6.13
- **Java**: 8
- **Apache Kafka**: 3.8.1+
- **监控**: Micrometer + Prometheus + Grafana
- **容器化**: Docker + Docker Compose

## 🏗️ 项目结构

```
kafka-order-service/
├── src/main/java/com/example/kafka/order/
│   ├── KafkaOrderServiceApplication.java     # 主应用类
│   ├── config/                               # 配置类
│   │   ├── KafkaProducerConfig.java         # Kafka生产者配置
│   │   ├── KafkaConsumerConfig.java         # Kafka消费者配置
│   │   └── MonitoringConfig.java            # 监控配置
│   ├── controller/                           # REST控制器
│   │   └── OrderController.java             # 订单API
│   ├── dto/                                  # 数据传输对象
│   │   ├── CreateOrderRequest.java
│   │   └── OrderResponse.java
│   ├── entity/                               # 实体类
│   │   ├── Order.java                       # 订单实体
│   │   ├── OrderStatus.java                 # 订单状态枚举
│   │   ├── OrderItem.java                   # 订单商品项
│   │   └── ShippingAddress.java             # 收货地址
│   ├── service/                              # 业务服务
│   │   ├── OrderProducerService.java        # 订单生产者服务
│   │   ├── OrderConsumerService.java        # 订单消费者服务
│   │   └── OrderProcessingService.java      # 订单处理服务
│   └── exception/                            # 异常处理
│       └── GlobalExceptionHandler.java      # 全局异常处理器
├── src/main/resources/
│   ├── application.yml                       # 应用配置
│   ├── application-prod.yml                  # 生产环境配置
│   └── logback-spring.xml                   # 日志配置
├── docker/
│   └── entrypoint.sh                        # Docker启动脚本
├── monitoring/                               # 监控配置
│   ├── prometheus.yml                       # Prometheus配置
│   └── grafana/                             # Grafana配置
├── Dockerfile                               # Docker构建文件
├── docker-compose.yml                       # Docker编排文件
└── pom.xml                                  # Maven配置
```

## 🚀 快速开始

### 前置条件

- Java 8+
- Maven 3.6+
- Docker & Docker Compose (可选)

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd kafka-order-service
```

2. **启动Kafka**
```bash
# 使用Docker Compose启动Kafka
docker-compose up -d kafka zookeeper
```

3. **编译项目**
```bash
mvn clean compile
```

4. **运行应用**
```bash
mvn spring-boot:run
```

### Docker部署

1. **构建并启动所有服务**
```bash
docker-compose up -d
```

2. **查看服务状态**
```bash
docker-compose ps
```

3. **查看日志**
```bash
docker-compose logs -f kafka-order-service
```

## 📡 API接口

### 创建订单
```bash
POST /api/v1/orders
Content-Type: application/json

{
  "user_id": "user123",
  "items": [
    {
      "product_id": "prod001",
      "product_name": "商品名称",
      "quantity": 2,
      "unit_price": 99.99
    }
  ],
  "shipping_address": {
    "recipient_name": "张三",
    "phone": "13800138000",
    "detail_address": "北京市朝阳区xxx街道xxx号"
  },
  "payment_method": "ALIPAY"
}
```

### 更新订单状态
```bash
PUT /api/v1/orders/{orderId}/status?status=PAID&remarks=支付成功
```

### 取消订单
```bash
PUT /api/v1/orders/{orderId}/cancel?reason=用户主动取消
```

### 批量创建订单（测试用）
```bash
POST /api/v1/orders/batch?count=100&userId=test-user
```

### 健康检查
```bash
GET /api/v1/orders/health
GET /actuator/health
```

## 📊 监控和指标

### Prometheus指标
- `kafka.order.sent.total` - 发送的订单总数
- `kafka.order.sent.success` - 成功发送的订单数
- `kafka.order.sent.failure` - 发送失败的订单数
- `kafka.order.received.total` - 接收的订单总数
- `kafka.order.processed.success` - 成功处理的订单数
- `kafka.order.processed.failure` - 处理失败的订单数
- `kafka.order.send.duration` - 发送耗时
- `kafka.order.processing.duration` - 处理耗时

### 访问监控界面
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin)
- **Kafka UI**: http://localhost:8081

## ⚙️ 配置说明

### Kafka主题配置
```yaml
app:
  kafka:
    topics:
      order-events: order-events              # 订单事件主题
      order-status-updates: order-status-updates  # 订单状态更新主题
      order-dlq: order-dlq                    # 死信队列主题
```

### 性能调优参数
```yaml
spring:
  kafka:
    producer:
      batch-size: 32768          # 批处理大小
      linger-ms: 10              # 批处理等待时间
      buffer-memory: 67108864    # 缓冲区大小
      compression-type: lz4      # 压缩算法
    
    consumer:
      max-poll-records: 500      # 每次拉取消息数
      concurrency: 3             # 并发消费者数
```

## 🔧 生产环境部署

### 环境变量配置
```bash
# Kafka集群配置
KAFKA_BOOTSTRAP_SERVERS=kafka1:9092,kafka2:9092,kafka3:9092
KAFKA_CONSUMER_GROUP_ID=order-service-prod-group
KAFKA_LISTENER_CONCURRENCY=10

# 安全配置（如果需要）
KAFKA_SECURITY_PROTOCOL=SASL_SSL
KAFKA_SSL_TRUSTSTORE_LOCATION=/path/to/truststore.jks
KAFKA_SSL_TRUSTSTORE_PASSWORD=password

# JVM配置
JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

### 生产环境启动
```bash
# 使用生产环境配置
docker run -d \
  --name kafka-order-service \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e KAFKA_BOOTSTRAP_SERVERS=kafka1:9092,kafka2:9092,kafka3:9092 \
  -e JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC" \
  kafka-order-service:1.0.0
```

## 🧪 测试

### 单元测试
```bash
mvn test
```

### 集成测试
```bash
mvn verify
```

### 性能测试
```bash
# 批量创建1000个订单
curl -X POST "http://localhost:8080/api/v1/orders/batch?count=1000&userId=perf-test"
```

## 📝 日志

### 日志文件位置
- 应用日志: `logs/kafka-order-service.log`
- Kafka日志: `logs/kafka-order-service-kafka.log`
- 错误日志: `logs/kafka-order-service-error.log`

### 日志格式
使用结构化JSON格式，便于日志聚合和分析。

## 🚨 故障排除

### 常见问题

1. **Kafka连接失败**
   - 检查Kafka服务是否启动
   - 验证bootstrap-servers配置
   - 检查网络连接

2. **消息发送失败**
   - 查看生产者配置
   - 检查主题是否存在
   - 验证序列化配置

3. **消费者无法接收消息**
   - 检查消费者组配置
   - 验证主题订阅
   - 查看偏移量管理

### 日志查看
```bash
# 查看应用日志
tail -f logs/kafka-order-service.log

# 查看错误日志
tail -f logs/kafka-order-service-error.log

# Docker环境查看日志
docker-compose logs -f kafka-order-service
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

MIT License
