#!/bin/bash

# WSL环境运行脚本
# 针对WSL环境优化的启动脚本

set -e

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${GREEN}🚀 在WSL环境中启动Kafka Order Service${NC}"

# 检查WSL环境
if grep -q microsoft /proc/version; then
    echo -e "${GREEN}✅ 检测到WSL环境${NC}"
else
    echo -e "${YELLOW}⚠️  未检测到WSL环境，但仍可运行${NC}"
fi

# WSL优化的JVM参数
WSL_JAVA_OPTS="-Xms512m -Xmx1g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+UseStringDeduplication \
  -Djava.security.egd=file:/dev/./urandom"

echo -e "${GREEN}📋 WSL优化配置:${NC}"
echo "   - 堆内存: 512MB-1GB"
echo "   - GC算法: G1GC"
echo "   - 最大GC暂停: 200ms"

# 检查Kafka是否运行
echo -e "${GREEN}🔍 检查Kafka状态...${NC}"
if nc -z localhost 9092; then
    echo -e "${GREEN}✅ Kafka运行正常${NC}"
else
    echo -e "${YELLOW}⚠️  Kafka未运行，尝试启动...${NC}"
    
    # 尝试启动Kafka
    if [ -f "/home/<USER>/code/ai/kafka_example/start-kafka.sh" ]; then
        cd /home/<USER>/code/ai/kafka_example
        ./start-kafka.sh
        sleep 10
        cd /home/<USER>/code/ai/kafka_product_consume
    else
        echo -e "${YELLOW}请手动启动Kafka服务${NC}"
        exit 1
    fi
fi

# 检查项目是否已编译
if [ ! -f "target/kafka-order-service-1.0.0.jar" ]; then
    echo -e "${GREEN}🔨 编译项目...${NC}"
    mvn clean package -DskipTests
fi

# 启动应用
echo -e "${GREEN}🚀 启动应用...${NC}"
echo "访问地址: http://localhost:8080"
echo "健康检查: http://localhost:8080/actuator/health"
echo "监控指标: http://localhost:8080/actuator/prometheus"
echo ""
echo "按 Ctrl+C 停止应用"
echo ""

# 使用WSL优化的参数启动
java $WSL_JAVA_OPTS -jar target/kafka-order-service-1.0.0.jar
