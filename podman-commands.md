# Podman 命令指南

## 🚀 在Windows中使用Podman运行监控

### 前置条件
确保您已经在Windows中安装了Podman Desktop或Podman CLI。

### 1. 启动监控栈

在Windows PowerShell或CMD中运行：

```powershell
# 进入项目目录
cd C:\path\to\your\kafka_example

# 创建网络
podman network create kafka-network

# 启动Prometheus
podman run -d `
  --name prometheus `
  --network kafka-network `
  -p 9090:9090 `
  -v "${PWD}/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro" `
  -v prometheus-data:/prometheus `
  prom/prometheus:latest `
  --config.file=/etc/prometheus/prometheus.yml `
  --storage.tsdb.path=/prometheus `
  --storage.tsdb.retention.time=200h `
  --web.enable-lifecycle

# 启动Grafana
podman run -d `
  --name grafana `
  --network kafka-network `
  -p 3000:3000 `
  -e GF_SECURITY_ADMIN_PASSWORD=admin `
  -v grafana-data:/var/lib/grafana `
  grafana/grafana:latest
```

### 2. 检查服务状态

```powershell
# 查看运行的容器
podman ps

# 查看容器日志
podman logs prometheus
podman logs grafana
```

### 3. 访问监控界面

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (用户名: admin, 密码: admin)

### 4. 配置Grafana

1. 登录Grafana (admin/admin)
2. 添加数据源:
   - Type: Prometheus
   - URL: http://prometheus:9090
3. 导入仪表板或创建自定义仪表板

### 5. 停止服务

```powershell
# 停止容器
podman stop prometheus grafana

# 删除容器
podman rm prometheus grafana

# 删除网络
podman network rm kafka-network
```

## 🔧 常用Podman命令

### 容器管理
```bash
# 查看所有容器
podman ps -a

# 查看容器日志
podman logs <container_name>

# 进入容器
podman exec -it <container_name> /bin/bash

# 重启容器
podman restart <container_name>
```

### 镜像管理
```bash
# 查看镜像
podman images

# 拉取镜像
podman pull <image_name>

# 删除镜像
podman rmi <image_name>
```

### 网络管理
```bash
# 查看网络
podman network ls

# 创建网络
podman network create <network_name>

# 删除网络
podman network rm <network_name>
```

### 卷管理
```bash
# 查看卷
podman volume ls

# 创建卷
podman volume create <volume_name>

# 删除卷
podman volume rm <volume_name>
```

## 📊 监控配置

### Prometheus配置文件 (monitoring/prometheus.yml)

```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'kafka-order-service'
    static_configs:
      - targets: ['host.containers.internal:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s
```

### Grafana仪表板查询示例

```promql
# 订单发送速率
rate(kafka_order_sent_total[5m])

# 订单处理延迟
histogram_quantile(0.95, rate(kafka_order_processing_duration_seconds_bucket[5m]))

# 成功率
kafka_order_sent_success_total / kafka_order_sent_total * 100
```

## 🐛 故障排除

### 常见问题

1. **容器无法访问主机服务**
   - 使用 `host.containers.internal` 而不是 `localhost`

2. **端口冲突**
   - 检查端口是否被占用: `netstat -an | findstr :9090`

3. **卷挂载问题**
   - 确保路径正确，使用绝对路径

4. **网络连接问题**
   - 检查防火墙设置
   - 确保容器在同一网络中

### 调试命令

```bash
# 检查容器网络
podman inspect <container_name> | grep -A 10 NetworkSettings

# 测试网络连接
podman exec -it <container_name> ping <target>

# 查看详细日志
podman logs --follow <container_name>
```
