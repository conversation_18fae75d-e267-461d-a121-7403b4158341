# 生产环境配置
server:
  port: 8080
  tomcat:
    threads:
      max: 500
      min-spare: 50
    max-connections: 20000
    accept-count: 200

spring:
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:kafka-cluster-1:9092,kafka-cluster-2:9092,kafka-cluster-3:9092}
    
    producer:
      # 生产环境性能优化
      batch-size: 65536          # 64KB批处理大小
      linger-ms: 5               # 减少延迟到5ms
      buffer-memory: 134217728   # 128MB缓冲区
      compression-type: lz4      # LZ4压缩
      
      # 高可靠性配置
      acks: all
      retries: 5                 # 增加重试次数
      enable-idempotence: true
      max-in-flight-requests-per-connection: 5
      
      # 生产环境超时配置
      request-timeout-ms: 60000
      delivery-timeout-ms: 300000
      
      # 安全配置（如果需要）
      security:
        protocol: ${KAFKA_SECURITY_PROTOCOL:PLAINTEXT}
      ssl:
        trust-store-location: ${KAFKA_SSL_TRUSTSTORE_LOCATION:}
        trust-store-password: ${KAFKA_SSL_TRUSTSTORE_PASSWORD:}
        key-store-location: ${KAFKA_SSL_KEYSTORE_LOCATION:}
        key-store-password: ${KAFKA_SSL_KEYSTORE_PASSWORD:}
    
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP_ID:order-service-prod-group}
      auto-offset-reset: earliest
      enable-auto-commit: false
      
      # 生产环境性能优化
      max-poll-records: 1000     # 增加批处理大小
      max-poll-interval-ms: 600000  # 10分钟最大轮询间隔
      session-timeout-ms: 45000     # 45秒会话超时
      heartbeat-interval-ms: 15000  # 15秒心跳间隔
      fetch-min-size: 4096          # 最小拉取4KB
      fetch-max-wait: 1000          # 最大等待1秒
      
      # 安全配置
      security:
        protocol: ${KAFKA_SECURITY_PROTOCOL:PLAINTEXT}
      ssl:
        trust-store-location: ${KAFKA_SSL_TRUSTSTORE_LOCATION:}
        trust-store-password: ${KAFKA_SSL_TRUSTSTORE_PASSWORD:}
    
    listener:
      concurrency: ${KAFKA_LISTENER_CONCURRENCY:10}  # 增加并发数
      poll-timeout: 5000

# 应用配置
app:
  kafka:
    topics:
      order-events: ${KAFKA_TOPIC_ORDER_EVENTS:order-events}
      order-status-updates: ${KAFKA_TOPIC_ORDER_STATUS_UPDATES:order-status-updates}
      order-dlq: ${KAFKA_TOPIC_ORDER_DLQ:order-dlq}

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
        step: 30s
    distribution:
      percentiles-histogram:
        http.server.requests: true
        kafka.order.send.duration: true
        kafka.order.processing.duration: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
        kafka.order.send.duration: 0.5,0.9,0.95,0.99
        kafka.order.processing.duration: 0.5,0.9,0.95,0.99

# 生产环境日志配置
logging:
  level:
    root: WARN
    com.example.kafka.order: INFO
    org.springframework.kafka: ERROR
    org.apache.kafka: ERROR
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
  file:
    name: /var/log/kafka-order-service/application.log
    max-size: 500MB
    max-history: 60
  logback:
    rollingpolicy:
      max-file-size: 500MB
      total-size-cap: 10GB

# JVM配置建议（通过环境变量设置）
# JAVA_OPTS: "-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/kafka-order-service/"
