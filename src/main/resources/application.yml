server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    threads:
      max: 200
      min-spare: 10
    max-connections: 8192
    accept-count: 100

spring:
  application:
    name: kafka-order-service
  
  profiles:
    active: dev
  
  kafka:
    bootstrap-servers: localhost:9092
    
    producer:
      # 性能优化配置
      batch-size: 32768          # 32KB批处理大小
      linger-ms: 10              # 等待10ms收集更多消息
      buffer-memory: 67108864    # 64MB缓冲区
      compression-type: lz4      # 使用LZ4压缩
      
      # 可靠性配置
      acks: all                  # 等待所有副本确认
      retries: 3                 # 重试3次
      enable-idempotence: true   # 启用幂等性
      max-in-flight-requests-per-connection: 5
      
      # 超时配置
      request-timeout-ms: 30000
      delivery-timeout-ms: 120000
    
    consumer:
      group-id: order-service-group
      auto-offset-reset: earliest
      enable-auto-commit: false  # 手动提交偏移量
      
      # 性能优化配置
      max-poll-records: 500      # 每次拉取最多500条消息
      max-poll-interval-ms: 300000  # 5分钟最大轮询间隔
      session-timeout-ms: 30000     # 30秒会话超时
      heartbeat-interval-ms: 10000  # 10秒心跳间隔
      fetch-min-size: 1024          # 最小拉取1KB
      fetch-max-wait: 500           # 最大等待500ms
    
    listener:
      concurrency: 3             # 3个并发消费者线程
      poll-timeout: 3000         # 3秒轮询超时

# 应用配置
app:
  kafka:
    topics:
      order-events: order-events
      order-status-updates: order-status-updates
      order-dlq: order-dlq

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
        kafka.order.send.duration: true
        kafka.order.processing.duration: true

# 日志配置
logging:
  level:
    com.example.kafka.order: INFO
    org.springframework.kafka: WARN
    org.apache.kafka: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
  file:
    name: logs/kafka-order-service.log
    max-size: 100MB
    max-history: 30
