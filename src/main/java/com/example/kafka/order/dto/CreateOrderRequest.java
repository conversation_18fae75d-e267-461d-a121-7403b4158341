package com.example.kafka.order.dto;

import com.example.kafka.order.entity.OrderItem;
import com.example.kafka.order.entity.ShippingAddress;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 创建订单请求DTO
 */
public class CreateOrderRequest {
    
    @NotBlank(message = "用户ID不能为空")
    @JsonProperty("user_id")
    private String userId;
    
    @NotEmpty(message = "订单商品不能为空")
    @Valid
    @JsonProperty("items")
    private List<OrderItem> items;
    
    @Valid
    @JsonProperty("shipping_address")
    private ShippingAddress shippingAddress;
    
    @JsonProperty("payment_method")
    private String paymentMethod;
    
    @JsonProperty("source")
    private String source = "WEB";
    
    @JsonProperty("remarks")
    private String remarks;
    
    @JsonProperty("coupon_code")
    private String couponCode;
    
    @JsonProperty("promotion_id")
    private String promotionId;

    // 默认构造函数
    public CreateOrderRequest() {}

    // 构造函数
    public CreateOrderRequest(String userId, List<OrderItem> items) {
        this.userId = userId;
        this.items = items;
    }

    // Getters and Setters
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<OrderItem> getItems() {
        return items;
    }

    public void setItems(List<OrderItem> items) {
        this.items = items;
    }

    public ShippingAddress getShippingAddress() {
        return shippingAddress;
    }

    public void setShippingAddress(ShippingAddress shippingAddress) {
        this.shippingAddress = shippingAddress;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public String getPromotionId() {
        return promotionId;
    }

    public void setPromotionId(String promotionId) {
        this.promotionId = promotionId;
    }

    @Override
    public String toString() {
        return "CreateOrderRequest{" +
                "userId='" + userId + '\'' +
                ", itemsCount=" + (items != null ? items.size() : 0) +
                ", paymentMethod='" + paymentMethod + '\'' +
                ", source='" + source + '\'' +
                '}';
    }
}
