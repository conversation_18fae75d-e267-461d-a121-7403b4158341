package com.example.kafka.order.service;

import com.example.kafka.order.entity.Order;
import com.example.kafka.order.entity.OrderStatus;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 订单消费者服务
 * 负责处理从Kafka接收的订单消息
 */
@Service
public class OrderConsumerService {

    private static final Logger logger = LoggerFactory.getLogger(OrderConsumerService.class);

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private OrderProcessingService orderProcessingService;

    // 用于幂等性处理的缓存
    private final ConcurrentHashMap<String, Long> processedOrders = new ConcurrentHashMap<>();
    
    // 监控指标
    private Counter orderReceivedCounter;
    private Counter orderProcessedSuccessCounter;
    private Counter orderProcessedFailureCounter;
    private Timer orderProcessingTimer;
    private AtomicLong duplicateOrdersCounter;

    @PostConstruct
    public void initMetrics() {
        orderReceivedCounter = Counter.builder("kafka.order.received.total")
                .description("Total number of orders received from Kafka")
                .register(meterRegistry);

        orderProcessedSuccessCounter = Counter.builder("kafka.order.processed.success")
                .description("Number of orders successfully processed")
                .register(meterRegistry);

        orderProcessedFailureCounter = Counter.builder("kafka.order.processed.failure")
                .description("Number of orders failed to process")
                .register(meterRegistry);

        orderProcessingTimer = Timer.builder("kafka.order.processing.duration")
                .description("Time taken to process order")
                .register(meterRegistry);

        duplicateOrdersCounter = new AtomicLong(0);
        meterRegistry.gauge("kafka.order.duplicates", duplicateOrdersCounter);
    }

    /**
     * 处理订单创建事件
     */
    @RetryableTopic(
        attempts = "3",
        backoff = @Backoff(delay = 1000, multiplier = 2.0),
        dltStrategy = org.springframework.kafka.retrytopic.DltStrategy.FAIL_ON_ERROR
    )
    @KafkaListener(
        topics = "${app.kafka.topics.order-events:order-events}",
        groupId = "${spring.kafka.consumer.group-id:order-service-group}",
        containerFactory = "orderKafkaListenerContainerFactory"
    )
    public void handleOrderCreatedEvent(
            @Payload Order order,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION_ID) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            ConsumerRecord<String, Order> record,
            Acknowledgment acknowledgment) {

        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            orderReceivedCounter.increment();
            
            logger.info("Received order created event: {} from topic: {}, partition: {}, offset: {}",
                    order.getOrderId(), topic, partition, offset);

            // 幂等性检查
            if (isDuplicateOrder(order)) {
                logger.warn("Duplicate order detected: {}, skipping processing", order.getOrderId());
                duplicateOrdersCounter.incrementAndGet();
                acknowledgment.acknowledge();
                return;
            }

            // 验证订单数据
            if (!isValidOrder(order)) {
                logger.error("Invalid order data: {}", order.getOrderId());
                orderProcessedFailureCounter.increment();
                acknowledgment.acknowledge(); // 确认消息，避免重复处理无效数据
                return;
            }

            // 处理订单创建
            processOrderCreated(order);

            // 记录处理成功
            markOrderAsProcessed(order);
            orderProcessedSuccessCounter.increment();
            
            logger.info("Successfully processed order created event: {}", order.getOrderId());

            // 手动确认消息
            acknowledgment.acknowledge();

        } catch (Exception e) {
            sample.stop(orderProcessingTimer);
            orderProcessedFailureCounter.increment();
            logger.error("Failed to process order created event: {}", order.getOrderId(), e);
            
            // 不确认消息，让Kafka重试
            throw new RuntimeException("Failed to process order: " + order.getOrderId(), e);
            
        } finally {
            sample.stop(orderProcessingTimer);
        }
    }

    /**
     * 处理订单状态更新事件
     */
    @KafkaListener(
        topics = "${app.kafka.topics.order-status-updates:order-status-updates}",
        groupId = "${spring.kafka.consumer.group-id:order-service-group}",
        containerFactory = "orderKafkaListenerContainerFactory"
    )
    public void handleOrderStatusUpdateEvent(
            @Payload Order order,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION_ID) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            Acknowledgment acknowledgment) {

        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            orderReceivedCounter.increment();
            
            logger.info("Received order status update event: {} with status: {} from topic: {}, partition: {}, offset: {}",
                    order.getOrderId(), order.getStatus(), topic, partition, offset);

            // 处理订单状态更新
            processOrderStatusUpdate(order);

            orderProcessedSuccessCounter.increment();
            logger.info("Successfully processed order status update event: {}", order.getOrderId());

            acknowledgment.acknowledge();

        } catch (Exception e) {
            sample.stop(orderProcessingTimer);
            orderProcessedFailureCounter.increment();
            logger.error("Failed to process order status update event: {}", order.getOrderId(), e);
            throw new RuntimeException("Failed to process order status update: " + order.getOrderId(), e);
            
        } finally {
            sample.stop(orderProcessingTimer);
        }
    }

    /**
     * 处理死信队列中的订单
     */
    @KafkaListener(
        topics = "${app.kafka.topics.order-dlq:order-dlq}",
        groupId = "${spring.kafka.consumer.group-id:order-service-group}-dlq",
        containerFactory = "orderKafkaListenerContainerFactory"
    )
    public void handleOrderDlqEvent(
            @Payload Order order,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            Acknowledgment acknowledgment) {

        try {
            logger.warn("Processing order from DLQ: {}", order.getOrderId());
            
            // 记录到监控系统或发送告警
            orderProcessedFailureCounter.increment();
            
            // 可以实现特殊的处理逻辑，比如人工干预
            handleFailedOrder(order);
            
            acknowledgment.acknowledge();
            
        } catch (Exception e) {
            logger.error("Failed to process order from DLQ: {}", order.getOrderId(), e);
            acknowledgment.acknowledge(); // 确认消息，避免DLQ中的消息无限重试
        }
    }

    /**
     * 检查是否为重复订单
     */
    private boolean isDuplicateOrder(Order order) {
        String key = order.getOrderId() + "_" + order.getVersion();
        return processedOrders.containsKey(key);
    }

    /**
     * 标记订单为已处理
     */
    private void markOrderAsProcessed(Order order) {
        String key = order.getOrderId() + "_" + order.getVersion();
        processedOrders.put(key, System.currentTimeMillis());
        
        // 清理过期的记录（保留1小时）
        long oneHourAgo = System.currentTimeMillis() - 3600000;
        processedOrders.entrySet().removeIf(entry -> entry.getValue() < oneHourAgo);
    }

    /**
     * 验证订单数据
     */
    private boolean isValidOrder(Order order) {
        return order != null &&
               order.getOrderId() != null &&
               order.getUserId() != null &&
               order.getStatus() != null &&
               order.getTotalAmount() != null &&
               order.getItems() != null &&
               !order.getItems().isEmpty();
    }

    /**
     * 处理订单创建
     */
    private void processOrderCreated(Order order) {
        logger.info("Processing order creation: {}", order.getOrderId());
        
        // 调用业务处理服务
        orderProcessingService.processNewOrder(order);
        
        logger.info("Order creation processed: {}", order.getOrderId());
    }

    /**
     * 处理订单状态更新
     */
    private void processOrderStatusUpdate(Order order) {
        logger.info("Processing order status update: {} to status: {}", 
                order.getOrderId(), order.getStatus());
        
        // 调用业务处理服务
        orderProcessingService.processOrderStatusUpdate(order);
        
        logger.info("Order status update processed: {}", order.getOrderId());
    }

    /**
     * 处理失败的订单
     */
    private void handleFailedOrder(Order order) {
        logger.error("Handling failed order: {}", order.getOrderId());
        
        // 可以实现以下逻辑：
        // 1. 发送告警通知
        // 2. 记录到数据库
        // 3. 触发人工处理流程
        
        orderProcessingService.handleFailedOrder(order);
    }
}
