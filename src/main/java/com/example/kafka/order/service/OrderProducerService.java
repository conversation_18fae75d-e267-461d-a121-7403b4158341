package com.example.kafka.order.service;

import com.example.kafka.order.entity.Order;
import com.example.kafka.order.entity.OrderStatus;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import javax.annotation.PostConstruct;
import java.util.concurrent.CompletableFuture;

/**
 * 订单生产者服务
 * 负责将订单消息发送到Kafka
 */
@Service
public class OrderProducerService {

    private static final Logger logger = LoggerFactory.getLogger(OrderProducerService.class);

    @Autowired
    private KafkaTemplate<String, Order> orderKafkaTemplate;

    @Autowired
    private MeterRegistry meterRegistry;

    @Value("${app.kafka.topics.order-events:order-events}")
    private String orderEventsTopic;

    @Value("${app.kafka.topics.order-status-updates:order-status-updates}")
    private String orderStatusUpdatesTopic;

    @Value("${app.kafka.topics.order-dlq:order-dlq}")
    private String orderDlqTopic;

    // 监控指标
    private Counter orderSentCounter;
    private Counter orderSentSuccessCounter;
    private Counter orderSentFailureCounter;
    private Timer orderSendTimer;

    @PostConstruct
    public void initMetrics() {
        orderSentCounter = Counter.builder("kafka.order.sent.total")
                .description("Total number of orders sent to Kafka")
                .register(meterRegistry);

        orderSentSuccessCounter = Counter.builder("kafka.order.sent.success")
                .description("Number of orders successfully sent to Kafka")
                .register(meterRegistry);

        orderSentFailureCounter = Counter.builder("kafka.order.sent.failure")
                .description("Number of orders failed to send to Kafka")
                .register(meterRegistry);

        orderSendTimer = Timer.builder("kafka.order.send.duration")
                .description("Time taken to send order to Kafka")
                .register(meterRegistry);
    }

    /**
     * 异步发送订单创建事件
     */
    public CompletableFuture<SendResult<String, Order>> sendOrderCreatedEvent(Order order) {
        return sendOrderEvent(orderEventsTopic, order, "ORDER_CREATED");
    }

    /**
     * 异步发送订单状态更新事件
     */
    public CompletableFuture<SendResult<String, Order>> sendOrderStatusUpdateEvent(Order order) {
        return sendOrderEvent(orderStatusUpdatesTopic, order, "ORDER_STATUS_UPDATED");
    }

    /**
     * 发送订单事件到指定主题
     */
    private CompletableFuture<SendResult<String, Order>> sendOrderEvent(String topic, Order order, String eventType) {
        Timer.Sample sample = Timer.start(meterRegistry);
        CompletableFuture<SendResult<String, Order>> future = new CompletableFuture<>();

        try {
            // 记录发送指标
            orderSentCounter.increment();

            // 使用订单ID作为分区键，确保同一订单的消息有序
            String partitionKey = order.getOrderId();

            logger.info("Sending {} event for order: {}", eventType, order.getOrderId());

            // 异步发送消息
            ListenableFuture<SendResult<String, Order>> kafkaFuture = 
                orderKafkaTemplate.send(topic, partitionKey, order);

            kafkaFuture.addCallback(new ListenableFutureCallback<SendResult<String, Order>>() {
                @Override
                public void onSuccess(SendResult<String, Order> result) {
                    sample.stop(orderSendTimer);
                    orderSentSuccessCounter.increment();
                    
                    logger.info("Successfully sent {} event for order: {} to partition: {} with offset: {}",
                            eventType, order.getOrderId(),
                            result.getRecordMetadata().partition(),
                            result.getRecordMetadata().offset());
                    
                    future.complete(result);
                }

                @Override
                public void onFailure(Throwable ex) {
                    sample.stop(orderSendTimer);
                    orderSentFailureCounter.increment();
                    
                    logger.error("Failed to send {} event for order: {}", eventType, order.getOrderId(), ex);
                    
                    // 发送失败时，可以考虑发送到DLQ
                    handleSendFailure(order, ex);
                    
                    future.completeExceptionally(ex);
                }
            });

        } catch (Exception e) {
            sample.stop(orderSendTimer);
            orderSentFailureCounter.increment();
            logger.error("Exception occurred while sending {} event for order: {}", eventType, order.getOrderId(), e);
            future.completeExceptionally(e);
        }

        return future;
    }

    /**
     * 处理发送失败的情况
     */
    private void handleSendFailure(Order order, Throwable ex) {
        try {
            // 可以将失败的消息发送到死信队列
            logger.warn("Attempting to send failed order to DLQ: {}", order.getOrderId());
            
            // 修改订单状态为失败
            Order failedOrder = new Order();
            failedOrder.setOrderId(order.getOrderId());
            failedOrder.setUserId(order.getUserId());
            failedOrder.setStatus(OrderStatus.FAILED);
            failedOrder.setRemarks("Kafka send failed: " + ex.getMessage());
            
            orderKafkaTemplate.send(orderDlqTopic, order.getOrderId(), failedOrder);
            
        } catch (Exception dlqEx) {
            logger.error("Failed to send order to DLQ: {}", order.getOrderId(), dlqEx);
        }
    }

    /**
     * 同步发送订单事件（用于关键业务场景）
     */
    public SendResult<String, Order> sendOrderEventSync(String topic, Order order) throws Exception {
        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            orderSentCounter.increment();
            
            logger.info("Synchronously sending order event for order: {}", order.getOrderId());
            
            SendResult<String, Order> result = orderKafkaTemplate.send(topic, order.getOrderId(), order).get();
            
            sample.stop(orderSendTimer);
            orderSentSuccessCounter.increment();
            
            logger.info("Successfully sent order event for order: {} to partition: {} with offset: {}",
                    order.getOrderId(),
                    result.getRecordMetadata().partition(),
                    result.getRecordMetadata().offset());
            
            return result;
            
        } catch (Exception e) {
            sample.stop(orderSendTimer);
            orderSentFailureCounter.increment();
            logger.error("Failed to synchronously send order event for order: {}", order.getOrderId(), e);
            throw e;
        }
    }

    /**
     * 批量发送订单事件
     */
    public void sendOrderEventsBatch(String topic, java.util.List<Order> orders) {
        for (Order order : orders) {
            sendOrderEvent(topic, order, "BATCH_ORDER_EVENT");
        }
        
        // 强制刷新缓冲区
        orderKafkaTemplate.flush();
        
        logger.info("Sent batch of {} orders to topic: {}", orders.size(), topic);
    }
}
