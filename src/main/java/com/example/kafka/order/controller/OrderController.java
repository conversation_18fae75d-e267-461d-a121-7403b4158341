package com.example.kafka.order.controller;

import com.example.kafka.order.dto.CreateOrderRequest;
import com.example.kafka.order.dto.OrderResponse;
import com.example.kafka.order.entity.Order;
import com.example.kafka.order.entity.OrderStatus;
import com.example.kafka.order.service.OrderProducerService;
import io.micrometer.core.annotation.Timed;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 订单控制器
 * 提供订单相关的REST API
 */
@RestController
@RequestMapping("/api/v1/orders")
@Validated
public class OrderController {

    private static final Logger logger = LoggerFactory.getLogger(OrderController.class);

    @Autowired
    private OrderProducerService orderProducerService;

    /**
     * 创建订单
     */
    @PostMapping
    @Timed(value = "order.create", description = "Time taken to create order")
    public ResponseEntity<OrderResponse> createOrder(@Valid @RequestBody CreateOrderRequest request) {
        try {
            logger.info("Received create order request for user: {}", request.getUserId());

            // 生成订单ID
            String orderId = generateOrderId();

            // 计算订单总金额
            BigDecimal totalAmount = calculateTotalAmount(request);

            // 创建订单对象
            Order order = new Order();
            order.setOrderId(orderId);
            order.setUserId(request.getUserId());
            order.setStatus(OrderStatus.PENDING);
            order.setTotalAmount(totalAmount);
            order.setItems(request.getItems());
            order.setShippingAddress(request.getShippingAddress());
            order.setPaymentMethod(request.getPaymentMethod());
            order.setSource(request.getSource());
            order.setRemarks(request.getRemarks());
            order.setCreatedAt(LocalDateTime.now());
            order.setUpdatedAt(LocalDateTime.now());

            // 异步发送订单创建事件
            CompletableFuture<org.springframework.kafka.support.SendResult<String, Order>> future = 
                orderProducerService.sendOrderCreatedEvent(order);

            // 创建响应
            OrderResponse response = OrderResponse.success(
                orderId, 
                request.getUserId(), 
                OrderStatus.PENDING, 
                totalAmount
            );

            logger.info("Order created successfully: {}", orderId);

            return ResponseEntity.status(HttpStatus.CREATED).body(response);

        } catch (Exception e) {
            logger.error("Failed to create order for user: {}", request.getUserId(), e);
            
            OrderResponse errorResponse = OrderResponse.error(null, "订单创建失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 更新订单状态
     */
    @PutMapping("/{orderId}/status")
    @Timed(value = "order.status.update", description = "Time taken to update order status")
    public ResponseEntity<OrderResponse> updateOrderStatus(
            @PathVariable String orderId,
            @RequestParam OrderStatus status,
            @RequestParam(required = false) String remarks) {
        
        try {
            logger.info("Received update order status request: {} to {}", orderId, status);

            // 创建状态更新订单对象
            Order order = new Order();
            order.setOrderId(orderId);
            order.setStatus(status);
            order.setRemarks(remarks);
            order.setUpdatedAt(LocalDateTime.now());

            // 发送状态更新事件
            orderProducerService.sendOrderStatusUpdateEvent(order);

            // 创建响应
            OrderResponse response = new OrderResponse();
            response.setOrderId(orderId);
            response.setStatus(status);
            response.setMessage("订单状态更新成功");
            response.setUpdatedAt(LocalDateTime.now());

            logger.info("Order status updated successfully: {} to {}", orderId, status);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to update order status: {} to {}", orderId, status, e);
            
            OrderResponse errorResponse = OrderResponse.error(orderId, "订单状态更新失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 取消订单
     */
    @PutMapping("/{orderId}/cancel")
    @Timed(value = "order.cancel", description = "Time taken to cancel order")
    public ResponseEntity<OrderResponse> cancelOrder(
            @PathVariable String orderId,
            @RequestParam(required = false) String reason) {
        
        try {
            logger.info("Received cancel order request: {}", orderId);

            // 创建取消订单对象
            Order order = new Order();
            order.setOrderId(orderId);
            order.setStatus(OrderStatus.CANCELLED);
            order.setRemarks("订单取消原因: " + (reason != null ? reason : "用户主动取消"));
            order.setUpdatedAt(LocalDateTime.now());

            // 发送取消事件
            orderProducerService.sendOrderStatusUpdateEvent(order);

            // 创建响应
            OrderResponse response = new OrderResponse();
            response.setOrderId(orderId);
            response.setStatus(OrderStatus.CANCELLED);
            response.setMessage("订单取消成功");
            response.setUpdatedAt(LocalDateTime.now());

            logger.info("Order cancelled successfully: {}", orderId);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to cancel order: {}", orderId, e);
            
            OrderResponse errorResponse = OrderResponse.error(orderId, "订单取消失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 批量创建订单（用于压力测试）
     */
    @PostMapping("/batch")
    @Timed(value = "order.batch.create", description = "Time taken to create batch orders")
    public ResponseEntity<String> createBatchOrders(
            @RequestParam(defaultValue = "10") int count,
            @RequestParam(defaultValue = "test-user") String userId) {
        
        try {
            logger.info("Received batch create order request: {} orders for user: {}", count, userId);

            for (int i = 0; i < count; i++) {
                String orderId = generateOrderId();
                
                Order order = new Order();
                order.setOrderId(orderId);
                order.setUserId(userId + "-" + i);
                order.setStatus(OrderStatus.PENDING);
                order.setTotalAmount(BigDecimal.valueOf(100.00 + i));
                order.setSource("BATCH_TEST");
                order.setCreatedAt(LocalDateTime.now());
                order.setUpdatedAt(LocalDateTime.now());

                // 异步发送订单创建事件
                orderProducerService.sendOrderCreatedEvent(order);
            }

            String message = String.format("成功创建 %d 个批量订单", count);
            logger.info(message);

            return ResponseEntity.ok(message);

        } catch (Exception e) {
            logger.error("Failed to create batch orders", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("批量创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("Order service is healthy");
    }

    /**
     * 生成订单ID
     */
    private String generateOrderId() {
        return "ORD-" + System.currentTimeMillis() + "-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    /**
     * 计算订单总金额
     */
    private BigDecimal calculateTotalAmount(CreateOrderRequest request) {
        return request.getItems().stream()
                .map(item -> item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
