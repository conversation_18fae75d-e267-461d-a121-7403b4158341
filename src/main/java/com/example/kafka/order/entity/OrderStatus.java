package com.example.kafka.order.entity;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 订单状态枚举
 */
public enum OrderStatus {
    PENDING("PENDING", "待处理"),
    CONFIRMED("CONFIRMED", "已确认"),
    PAID("PAID", "已支付"),
    PROCESSING("PROCESSING", "处理中"),
    SHIPPED("SHIPPED", "已发货"),
    DELIVERED("DELIVERED", "已送达"),
    COMPLETED("COMPLETED", "已完成"),
    CANCELLED("CANCELLED", "已取消"),
    REFUNDED("REFUNDED", "已退款"),
    FAILED("FAILED", "失败");

    private final String code;
    private final String description;

    OrderStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @JsonValue
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @JsonCreator
    public static OrderStatus fromCode(String code) {
        for (OrderStatus status : OrderStatus.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown order status code: " + code);
    }

    /**
     * 检查是否可以转换到目标状态
     */
    public boolean canTransitionTo(OrderStatus targetStatus) {
        switch (this) {
            case PENDING:
                return targetStatus == CONFIRMED || targetStatus == CANCELLED;
            case CONFIRMED:
                return targetStatus == PAID || targetStatus == CANCELLED;
            case PAID:
                return targetStatus == PROCESSING || targetStatus == REFUNDED;
            case PROCESSING:
                return targetStatus == SHIPPED || targetStatus == FAILED;
            case SHIPPED:
                return targetStatus == DELIVERED || targetStatus == FAILED;
            case DELIVERED:
                return targetStatus == COMPLETED;
            case COMPLETED:
            case CANCELLED:
            case REFUNDED:
            case FAILED:
                return false; // 终态，不能再转换
            default:
                return false;
        }
    }

    /**
     * 检查是否为终态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == CANCELLED || 
               this == REFUNDED || this == FAILED;
    }

    @Override
    public String toString() {
        return code;
    }
}
