package com.example.kafka.order.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotBlank;
import java.util.Objects;

/**
 * 收货地址
 */
public class ShippingAddress {
    
    @NotBlank(message = "收货人姓名不能为空")
    @JsonProperty("recipient_name")
    private String recipientName;
    
    @NotBlank(message = "收货人电话不能为空")
    @JsonProperty("phone")
    private String phone;
    
    @JsonProperty("province")
    private String province;
    
    @JsonProperty("city")
    private String city;
    
    @JsonProperty("district")
    private String district;
    
    @NotBlank(message = "详细地址不能为空")
    @JsonProperty("detail_address")
    private String detailAddress;
    
    @JsonProperty("postal_code")
    private String postalCode;
    
    @JsonProperty("is_default")
    private Boolean isDefault = false;

    // 默认构造函数
    public ShippingAddress() {}

    // 构造函数
    public ShippingAddress(String recipientName, String phone, String detailAddress) {
        this.recipientName = recipientName;
        this.phone = phone;
        this.detailAddress = detailAddress;
    }

    // Getters and Setters
    public String getRecipientName() {
        return recipientName;
    }

    public void setRecipientName(String recipientName) {
        this.recipientName = recipientName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    /**
     * 获取完整地址
     */
    public String getFullAddress() {
        StringBuilder sb = new StringBuilder();
        if (province != null) sb.append(province);
        if (city != null) sb.append(city);
        if (district != null) sb.append(district);
        if (detailAddress != null) sb.append(detailAddress);
        return sb.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ShippingAddress that = (ShippingAddress) o;
        return Objects.equals(recipientName, that.recipientName) &&
               Objects.equals(phone, that.phone) &&
               Objects.equals(detailAddress, that.detailAddress);
    }

    @Override
    public int hashCode() {
        return Objects.hash(recipientName, phone, detailAddress);
    }

    @Override
    public String toString() {
        return "ShippingAddress{" +
                "recipientName='" + recipientName + '\'' +
                ", phone='" + phone + '\'' +
                ", fullAddress='" + getFullAddress() + '\'' +
                '}';
    }
}
