package com.example.kafka.order.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 订单商品项
 */
public class OrderItem {
    
    @NotBlank(message = "商品ID不能为空")
    @JsonProperty("product_id")
    private String productId;
    
    @NotBlank(message = "商品名称不能为空")
    @JsonProperty("product_name")
    private String productName;
    
    @JsonProperty("product_sku")
    private String productSku;
    
    @NotNull(message = "商品数量不能为空")
    @Positive(message = "商品数量必须大于0")
    @JsonProperty("quantity")
    private Integer quantity;
    
    @NotNull(message = "商品单价不能为空")
    @Positive(message = "商品单价必须大于0")
    @JsonProperty("unit_price")
    private BigDecimal unitPrice;
    
    @NotNull(message = "商品总价不能为空")
    @JsonProperty("total_price")
    private BigDecimal totalPrice;
    
    @JsonProperty("discount_amount")
    private BigDecimal discountAmount = BigDecimal.ZERO;
    
    @JsonProperty("category")
    private String category;
    
    @JsonProperty("brand")
    private String brand;
    
    @JsonProperty("specifications")
    private String specifications;

    // 默认构造函数
    public OrderItem() {}

    // 构造函数
    public OrderItem(String productId, String productName, Integer quantity, BigDecimal unitPrice) {
        this.productId = productId;
        this.productName = productName;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.totalPrice = unitPrice.multiply(BigDecimal.valueOf(quantity));
    }

    // Getters and Setters
    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductSku() {
        return productSku;
    }

    public void setProductSku(String productSku) {
        this.productSku = productSku;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
        if (this.unitPrice != null) {
            this.totalPrice = this.unitPrice.multiply(BigDecimal.valueOf(quantity));
        }
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
        if (this.quantity != null) {
            this.totalPrice = unitPrice.multiply(BigDecimal.valueOf(this.quantity));
        }
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    /**
     * 计算实际支付金额（总价 - 折扣）
     */
    public BigDecimal getActualAmount() {
        return totalPrice.subtract(discountAmount);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OrderItem orderItem = (OrderItem) o;
        return Objects.equals(productId, orderItem.productId) &&
               Objects.equals(productSku, orderItem.productSku);
    }

    @Override
    public int hashCode() {
        return Objects.hash(productId, productSku);
    }

    @Override
    public String toString() {
        return "OrderItem{" +
                "productId='" + productId + '\'' +
                ", productName='" + productName + '\'' +
                ", quantity=" + quantity +
                ", unitPrice=" + unitPrice +
                ", totalPrice=" + totalPrice +
                '}';
    }
}
