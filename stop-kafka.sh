#!/bin/bash

# Kafka 停止脚本

KAFKA_HOME="$(pwd)/kafka"

echo "正在停止 Kafka 服务器..."

# 检查 Kafka 是否在运行
if ! pgrep -f "kafka.Kafka" > /dev/null; then
    echo "Kafka 服务器没有在运行。"
    exit 0
fi

# 使用 Kafka 自带的停止脚本
$KAFKA_HOME/bin/kafka-server-stop.sh

# 等待进程完全停止
echo "等待 Kafka 进程完全停止..."
sleep 5

# 检查是否还有 Kafka 进程在运行
if pgrep -f "kafka.Kafka" > /dev/null; then
    echo "正常停止失败，强制终止 Kafka 进程..."
    pkill -f "kafka.Kafka"
    sleep 2

    # 再次检查
    if pgrep -f "kafka.Kafka" > /dev/null; then
        echo "❌ 无法停止 Kafka 进程，请手动检查"
        exit 1
    fi
fi

echo "✅ Kafka 服务器已成功停止！"
