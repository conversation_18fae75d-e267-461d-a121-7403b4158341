# Timer.Sample 详解

## 🕐 Timer.Sample 的作用

`Timer.Sample` 是 Micrometer 中用于精确测量代码执行时间的工具，它能够：

1. **记录开始时间**：创建时记录当前时间戳
2. **计算执行时长**：调用 `stop()` 时计算时间差
3. **记录到指标**：将时长数据记录到 Timer 指标中
4. **生成统计信息**：自动计算平均值、百分位数等

## 🔧 基本用法

### 1. 标准用法模式

```java
// 1. 创建 Timer 指标
Timer orderProcessingTimer = Timer.builder("order.processing.duration")
    .description("Time taken to process an order")
    .register(meterRegistry);

// 2. 开始计时
Timer.Sample sample = Timer.start(meterRegistry);

try {
    // 3. 执行业务逻辑
    processOrder(order);
    
    // 4. 停止计时并记录到指标
    sample.stop(orderProcessingTimer);
    
} catch (Exception e) {
    // 5. 异常情况也要停止计时
    sample.stop(orderProcessingTimer);
    throw e;
}
```

### 2. 使用 try-finally 确保计时结束

```java
Timer.Sample sample = Timer.start(meterRegistry);
try {
    // 业务逻辑
    sendMessageToKafka(message);
} finally {
    // 无论成功还是失败都记录时间
    sample.stop(orderSendTimer);
}
```

## 📊 在项目中的实际应用

### 1. 订单发送计时 (OrderProducerService)

```java
private CompletableFuture<SendResult<String, Order>> sendOrderEvent(String topic, Order order, String eventType) {
    // 开始计时
    Timer.Sample sample = Timer.start(meterRegistry);
    
    try {
        // 发送消息到Kafka
        ListenableFuture<SendResult<String, Order>> kafkaFuture = 
            orderKafkaTemplate.send(topic, partitionKey, order);

        kafkaFuture.addCallback(new ListenableFutureCallback<SendResult<String, Order>>() {
            @Override
            public void onSuccess(SendResult<String, Order> result) {
                // 成功时停止计时
                sample.stop(orderSendTimer);
                orderSentSuccessCounter.increment();
            }

            @Override
            public void onFailure(Throwable ex) {
                // 失败时也要停止计时
                sample.stop(orderSendTimer);
                orderSentFailureCounter.increment();
            }
        });

    } catch (Exception e) {
        // 异常时停止计时
        sample.stop(orderSendTimer);
        orderSentFailureCounter.increment();
    }
}
```

### 2. 订单处理计时 (OrderConsumerService)

```java
@KafkaListener(topics = "${app.kafka.topics.order-events}")
public void handleOrderCreatedEvent(Order order, Acknowledgment acknowledgment) {
    // 开始计时
    Timer.Sample sample = Timer.start(meterRegistry);
    
    try {
        // 处理订单逻辑
        processOrderCreatedEvent(order);
        
        // 确认消息
        acknowledgment.acknowledge();
        
    } catch (Exception e) {
        // 异常处理
        orderProcessedFailureCounter.increment();
        throw new RuntimeException("Failed to process order: " + order.getOrderId(), e);
        
    } finally {
        // 无论成功失败都记录处理时间
        sample.stop(orderProcessingTimer);
    }
}
```

## 📈 生成的指标数据

### 1. Timer 指标包含的信息

```prometheus
# 总调用次数
kafka_order_send_duration_seconds_count{application="kafka-order-service"} 100.0

# 总耗时（秒）
kafka_order_send_duration_seconds_sum{application="kafka-order-service"} 5.234

# 平均耗时 = sum / count
# 平均耗时 = 5.234 / 100 = 0.05234 秒 = 52.34 毫秒

# 直方图桶（用于计算百分位数）
kafka_order_send_duration_seconds_bucket{le="0.001"} 10.0
kafka_order_send_duration_seconds_bucket{le="0.01"} 50.0
kafka_order_send_duration_seconds_bucket{le="0.1"} 95.0
kafka_order_send_duration_seconds_bucket{le="1.0"} 100.0
kafka_order_send_duration_seconds_bucket{le="+Inf"} 100.0
```

### 2. 可以计算的统计信息

- **平均耗时**: `sum / count`
- **QPS**: `rate(count[5m])`
- **P95延迟**: `histogram_quantile(0.95, rate(bucket[5m]))`
- **P99延迟**: `histogram_quantile(0.99, rate(bucket[5m]))`

## 🎯 最佳实践

### 1. 确保总是调用 stop()

```java
// ❌ 错误：可能忘记调用 stop()
Timer.Sample sample = Timer.start(meterRegistry);
if (condition) {
    sample.stop(timer);  // 只在某些情况下调用
    return;
}
// 忘记调用 stop()

// ✅ 正确：使用 try-finally
Timer.Sample sample = Timer.start(meterRegistry);
try {
    // 业务逻辑
} finally {
    sample.stop(timer);  // 总是调用
}
```

### 2. 区分成功和失败的计时

```java
Timer.Sample sample = Timer.start(meterRegistry);
try {
    processOrder(order);
    sample.stop(successTimer);  // 成功计时器
} catch (Exception e) {
    sample.stop(failureTimer);  // 失败计时器
    throw e;
}
```

### 3. 使用标签区分不同场景

```java
Timer orderTimer = Timer.builder("order.processing.duration")
    .tag("operation", "create")  // 标签区分操作类型
    .tag("status", "success")    // 标签区分状态
    .register(meterRegistry);
```

## 🔍 调试和监控

### 1. 查看当前指标值

```bash
# 查看发送耗时指标
curl -s http://localhost:8080/actuator/prometheus | grep kafka_order_send_duration

# 查看处理耗时指标
curl -s http://localhost:8080/actuator/prometheus | grep kafka_order_processing_duration
```

### 2. Prometheus 查询示例

```promql
# 平均发送耗时（毫秒）
rate(kafka_order_send_duration_seconds_sum[5m]) / rate(kafka_order_send_duration_seconds_count[5m]) * 1000

# P95 发送延迟
histogram_quantile(0.95, rate(kafka_order_send_duration_seconds_bucket[5m]))

# 每秒发送订单数
rate(kafka_order_send_duration_seconds_count[5m])
```

## 💡 Timer.Sample vs 其他计时方式

### 1. Timer.Sample (推荐)

```java
Timer.Sample sample = Timer.start(meterRegistry);
// 业务逻辑
sample.stop(timer);
```

**优点**：
- 精确计时
- 自动处理时间单位转换
- 与 Micrometer 完美集成

### 2. Timer.recordCallable()

```java
String result = timer.recordCallable(() -> {
    // 业务逻辑
    return processOrder(order);
});
```

**优点**：
- 代码简洁
- 自动处理异常

### 3. 手动计时

```java
long start = System.nanoTime();
try {
    // 业务逻辑
} finally {
    timer.record(System.nanoTime() - start, TimeUnit.NANOSECONDS);
}
```

**缺点**：
- 容易出错
- 需要手动处理时间单位

## 🎯 总结

`Timer.Sample` 是测量代码执行时间的最佳工具，它：

1. **精确测量**：纳秒级精度
2. **自动统计**：生成平均值、百分位数等
3. **易于使用**：简单的 start/stop 模式
4. **异常安全**：配合 try-finally 使用
5. **监控友好**：与 Prometheus 完美集成

在我们的 Kafka 订单处理系统中，它帮助我们监控：
- 消息发送耗时
- 消息处理耗时
- 业务逻辑执行时间

这些指标对于性能优化和问题诊断非常重要！🚀
