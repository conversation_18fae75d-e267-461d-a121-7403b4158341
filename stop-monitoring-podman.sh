#!/bin/bash

# 停止Podman监控服务

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🛑 停止Kafka Order Service监控栈${NC}"

# 停止容器
echo -e "${YELLOW}📊 停止Prometheus...${NC}"
podman stop prometheus 2>/dev/null || echo "Prometheus未运行"
podman rm prometheus 2>/dev/null || echo "Prometheus容器不存在"

echo -e "${YELLOW}📈 停止Grafana...${NC}"
podman stop grafana 2>/dev/null || echo "Grafana未运行"
podman rm grafana 2>/dev/null || echo "Grafana容器不存在"

# 可选：删除网络（如果没有其他容器使用）
echo -e "${YELLOW}📡 清理网络...${NC}"
podman network rm kafka-network 2>/dev/null || echo "网络仍在使用或不存在"

echo -e "${GREEN}✅ 监控栈已停止${NC}"
