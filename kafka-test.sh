#!/bin/bash

# Kafka 测试脚本
# 测试生产者和消费者功能

KAFKA_HOME="$(pwd)/kafka"
BOOTSTRAP_SERVER="localhost:9092"
TEST_TOPIC="kafka-test-topic"

echo "🧪 Kafka 功能测试脚本"
echo "========================"

# 检查 Kafka 是否在运行
if ! pgrep -f "kafka.server.KafkaRaftServer" > /dev/null; then
    echo "❌ Kafka 服务器没有运行，请先启动 Kafka"
    echo "运行: ./start-kafka.sh"
    exit 1
fi

echo "✅ Kafka 服务器正在运行"

# 创建测试主题
echo "📝 创建测试主题: $TEST_TOPIC"
$KAFKA_HOME/bin/kafka-topics.sh --create --topic $TEST_TOPIC --bootstrap-server $BOOTSTRAP_SERVER --partitions 3 --replication-factor 1 --if-not-exists

# 列出所有主题
echo ""
echo "📋 当前所有主题:"
$KAFKA_HOME/bin/kafka-topics.sh --list --bootstrap-server $BOOTSTRAP_SERVER

# 查看主题详情
echo ""
echo "🔍 主题 $TEST_TOPIC 详情:"
$KAFKA_HOME/bin/kafka-topics.sh --describe --topic $TEST_TOPIC --bootstrap-server $BOOTSTRAP_SERVER

echo ""
echo "🎯 测试说明:"
echo "1. 生产者测试: echo 'Hello Kafka' | $KAFKA_HOME/bin/kafka-console-producer.sh --topic $TEST_TOPIC --bootstrap-server $BOOTSTRAP_SERVER"
echo "2. 消费者测试: $KAFKA_HOME/bin/kafka-console-consumer.sh --topic $TEST_TOPIC --from-beginning --bootstrap-server $BOOTSTRAP_SERVER"
echo ""
echo "💡 提示: 在不同的终端窗口中运行生产者和消费者来测试消息传递"
