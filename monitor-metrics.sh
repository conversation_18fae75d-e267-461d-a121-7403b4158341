#!/bin/bash

# Kafka Order Service 监控脚本
# 实时显示关键指标

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'
BOLD='\033[1m'

# 服务URL
SERVICE_URL="http://localhost:8080"

# 清屏函数
clear_screen() {
    clear
    echo -e "${BOLD}${BLUE}🚀 Kafka Order Service 实时监控${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

# 获取指标值
get_metric() {
    local metric_name=$1
    curl -s "$SERVICE_URL/actuator/prometheus" | grep "^$metric_name{" | tail -1 | awk '{print $2}' || echo "0"
}

# 获取直方图指标
get_histogram_metric() {
    local metric_name=$1
    local suffix=$2
    curl -s "$SERVICE_URL/actuator/prometheus" | grep "^${metric_name}_${suffix}{" | tail -1 | awk '{print $2}' || echo "0"
}

# 计算平均值
calculate_average() {
    local sum=$1
    local count=$2
    if [ "$count" != "0" ] && [ "$count" != "0.0" ]; then
        echo "scale=2; $sum / $count * 1000" | bc -l
    else
        echo "0"
    fi
}

# 格式化数字
format_number() {
    local num=$1
    printf "%.0f" "$num" 2>/dev/null || echo "0"
}

# 显示指标
show_metrics() {
    # 获取基础指标
    local orders_sent=$(get_metric "kafka_order_sent_total")
    local orders_sent_success=$(get_metric "kafka_order_sent_success_total")
    local orders_sent_failure=$(get_metric "kafka_order_sent_failure_total")
    local orders_received=$(get_metric "kafka_order_received_total")
    local orders_processed_success=$(get_metric "kafka_order_processed_success_total")
    local orders_processed_failure=$(get_metric "kafka_order_processed_failure_total")
    local duplicate_orders=$(get_metric "kafka_order_duplicates")

    # 获取延迟指标
    local send_sum=$(get_histogram_metric "kafka_order_send_duration_seconds" "sum")
    local send_count=$(get_histogram_metric "kafka_order_send_duration_seconds" "count")
    local process_sum=$(get_histogram_metric "kafka_order_processing_duration_seconds" "sum")
    local process_count=$(get_histogram_metric "kafka_order_processing_duration_seconds" "count")

    # 计算平均延迟
    local avg_send_latency=$(calculate_average "$send_sum" "$send_count")
    local avg_process_latency=$(calculate_average "$process_sum" "$process_count")

    # 计算成功率
    local send_success_rate="0"
    if [ "$orders_sent" != "0" ]; then
        send_success_rate=$(echo "scale=1; $orders_sent_success / $orders_sent * 100" | bc -l)
    fi

    local process_success_rate="0"
    if [ "$orders_received" != "0" ]; then
        process_success_rate=$(echo "scale=1; $orders_processed_success / $orders_received * 100" | bc -l)
    fi

    # 显示指标
    echo -e "${BOLD}📊 核心指标${NC}"
    echo "┌─────────────────────────────────────────────────────────────┐"
    printf "│ %-25s │ %10s │ %15s │\n" "指标名称" "当前值" "描述"
    echo "├─────────────────────────────────────────────────────────────┤"
    printf "│ %-25s │ %10s │ %15s │\n" "发送订单总数" "$(format_number $orders_sent)" "已发送到Kafka"
    printf "│ %-25s │ %10s │ %15s │\n" "发送成功数" "$(format_number $orders_sent_success)" "成功发送"
    printf "│ %-25s │ %10s │ %15s │\n" "发送失败数" "$(format_number $orders_sent_failure)" "发送失败"
    printf "│ %-25s │ %10s │ %15s │\n" "接收订单总数" "$(format_number $orders_received)" "从Kafka接收"
    printf "│ %-25s │ %10s │ %15s │\n" "处理成功数" "$(format_number $orders_processed_success)" "成功处理"
    printf "│ %-25s │ %10s │ %15s │\n" "处理失败数" "$(format_number $orders_processed_failure)" "处理失败"
    printf "│ %-25s │ %10s │ %15s │\n" "重复订单数" "$(format_number $duplicate_orders)" "重复检测"
    echo "└─────────────────────────────────────────────────────────────┘"

    echo ""
    echo -e "${BOLD}⚡ 性能指标${NC}"
    echo "┌─────────────────────────────────────────────────────────────┐"
    printf "│ %-25s │ %10s │ %15s │\n" "平均发送延迟" "$(format_number $avg_send_latency) ms" "消息发送耗时"
    printf "│ %-25s │ %10s │ %15s │\n" "平均处理延迟" "$(format_number $avg_process_latency) ms" "消息处理耗时"
    printf "│ %-25s │ %10s │ %15s │\n" "发送成功率" "$(format_number $send_success_rate)%" "发送成功比例"
    printf "│ %-25s │ %10s │ %15s │\n" "处理成功率" "$(format_number $process_success_rate)%" "处理成功比例"
    echo "└─────────────────────────────────────────────────────────────┘"

    echo ""
    echo -e "${BOLD}🔗 监控端点${NC}"
    echo "• 健康检查: ${BLUE}$SERVICE_URL/actuator/health${NC}"
    echo "• Prometheus指标: ${BLUE}$SERVICE_URL/actuator/prometheus${NC}"
    echo "• 应用信息: ${BLUE}$SERVICE_URL/actuator/info${NC}"
    echo "• 监控仪表板: ${BLUE}file://$(pwd)/monitoring-dashboard.html${NC}"

    echo ""
    echo -e "${YELLOW}最后更新: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
    echo -e "${YELLOW}按 Ctrl+C 退出监控${NC}"
}

# 检查服务是否运行
check_service() {
    if ! curl -s -f "$SERVICE_URL/actuator/health" > /dev/null; then
        echo -e "${RED}❌ 服务未运行或不可访问${NC}"
        echo -e "${YELLOW}请确保 Kafka Order Service 在 $SERVICE_URL 运行${NC}"
        exit 1
    fi
}

# 主循环
main() {
    # 检查依赖
    if ! command -v bc &> /dev/null; then
        echo -e "${RED}错误: 需要安装 bc 计算器${NC}"
        echo "Ubuntu/Debian: sudo apt install bc"
        exit 1
    fi

    # 检查服务
    echo "检查服务状态..."
    check_service
    echo -e "${GREEN}✅ 服务运行正常${NC}"
    sleep 2

    # 实时监控循环
    while true; do
        clear_screen
        show_metrics
        sleep 5
    done
}

# 信号处理
trap 'echo -e "\n${GREEN}监控已停止${NC}"; exit 0' INT

# 显示帮助
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Kafka Order Service 监控脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help, -h     显示帮助信息"
    echo "  --once         只显示一次指标"
    echo ""
    echo "功能:"
    echo "  - 实时显示Kafka订单处理指标"
    echo "  - 自动刷新（每5秒）"
    echo "  - 计算成功率和平均延迟"
    echo ""
    exit 0
fi

# 只显示一次
if [ "$1" = "--once" ]; then
    check_service
    clear_screen
    show_metrics
    exit 0
fi

# 运行主程序
main
