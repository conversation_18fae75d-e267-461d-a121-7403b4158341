<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kafka Order Service 监控仪表板</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        .metric-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .metric-description {
            font-size: 12px;
            color: #888;
        }
        .success { border-left-color: #4CAF50; }
        .warning { border-left-color: #FF9800; }
        .error { border-left-color: #F44336; }
        .info { border-left-color: #2196F3; }
        
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .refresh-btn:hover {
            background: #5a6fd8;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-up { background-color: #4CAF50; }
        .status-down { background-color: #F44336; }
        
        .logs-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .endpoint-list {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .endpoint-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .endpoint-item:last-child {
            border-bottom: none;
        }
        
        .endpoint-link {
            color: #667eea;
            text-decoration: none;
            font-family: monospace;
        }
        
        .endpoint-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Kafka Order Service 监控仪表板</h1>
        <p>实时监控您的订单处理系统</p>
        <button class="refresh-btn" onclick="refreshMetrics()">🔄 刷新数据</button>
        <span id="lastUpdate"></span>
    </div>

    <div class="metrics-grid">
        <div class="metric-card success">
            <div class="metric-title">系统状态</div>
            <div class="metric-value" id="systemStatus">
                <span class="status-indicator status-up"></span>运行中
            </div>
            <div class="metric-description">应用程序健康状态</div>
        </div>

        <div class="metric-card info">
            <div class="metric-title">发送订单总数</div>
            <div class="metric-value" id="ordersSent">-</div>
            <div class="metric-description">已发送到Kafka的订单数量</div>
        </div>

        <div class="metric-card success">
            <div class="metric-title">发送成功数</div>
            <div class="metric-value" id="ordersSentSuccess">-</div>
            <div class="metric-description">成功发送的订单数量</div>
        </div>

        <div class="metric-card error">
            <div class="metric-title">发送失败数</div>
            <div class="metric-value" id="ordersSentFailure">-</div>
            <div class="metric-description">发送失败的订单数量</div>
        </div>

        <div class="metric-card info">
            <div class="metric-title">接收订单总数</div>
            <div class="metric-value" id="ordersReceived">-</div>
            <div class="metric-description">从Kafka接收的订单数量</div>
        </div>

        <div class="metric-card success">
            <div class="metric-title">处理成功数</div>
            <div class="metric-value" id="ordersProcessedSuccess">-</div>
            <div class="metric-description">成功处理的订单数量</div>
        </div>

        <div class="metric-card error">
            <div class="metric-title">处理失败数</div>
            <div class="metric-value" id="ordersProcessedFailure">-</div>
            <div class="metric-description">处理失败的订单数量</div>
        </div>

        <div class="metric-card warning">
            <div class="metric-title">重复订单数</div>
            <div class="metric-value" id="duplicateOrders">-</div>
            <div class="metric-description">检测到的重复订单数量</div>
        </div>

        <div class="metric-card info">
            <div class="metric-title">平均发送延迟</div>
            <div class="metric-value" id="avgSendLatency">-</div>
            <div class="metric-description">订单发送平均耗时 (ms)</div>
        </div>

        <div class="metric-card info">
            <div class="metric-title">平均处理延迟</div>
            <div class="metric-value" id="avgProcessLatency">-</div>
            <div class="metric-description">订单处理平均耗时 (ms)</div>
        </div>
    </div>

    <div class="endpoint-list">
        <h3>📡 监控端点</h3>
        <div class="endpoint-item">
            <span>健康检查</span>
            <a href="http://localhost:8080/actuator/health" target="_blank" class="endpoint-link">
                /actuator/health
            </a>
        </div>
        <div class="endpoint-item">
            <span>Prometheus指标</span>
            <a href="http://localhost:8080/actuator/prometheus" target="_blank" class="endpoint-link">
                /actuator/prometheus
            </a>
        </div>
        <div class="endpoint-item">
            <span>应用信息</span>
            <a href="http://localhost:8080/actuator/info" target="_blank" class="endpoint-link">
                /actuator/info
            </a>
        </div>
        <div class="endpoint-item">
            <span>所有指标</span>
            <a href="http://localhost:8080/actuator/metrics" target="_blank" class="endpoint-link">
                /actuator/metrics
            </a>
        </div>
    </div>

    <div class="endpoint-list">
        <h3>🔧 API端点</h3>
        <div class="endpoint-item">
            <span>创建订单</span>
            <span class="endpoint-link">POST /api/v1/orders</span>
        </div>
        <div class="endpoint-item">
            <span>批量创建订单</span>
            <span class="endpoint-link">POST /api/v1/orders/batch</span>
        </div>
        <div class="endpoint-item">
            <span>更新订单状态</span>
            <span class="endpoint-link">PUT /api/v1/orders/{id}/status</span>
        </div>
        <div class="endpoint-item">
            <span>取消订单</span>
            <span class="endpoint-link">PUT /api/v1/orders/{id}/cancel</span>
        </div>
    </div>

    <script>
        async function fetchMetrics() {
            try {
                const response = await fetch('/actuator/prometheus');
                const text = await response.text();
                return text;
            } catch (error) {
                console.error('获取指标失败:', error);
                return null;
            }
        }

        function parseMetric(metricsText, metricName) {
            const regex = new RegExp(`${metricName}\\{[^}]*\\}\\s+(\\d+(?:\\.\\d+)?)`);
            const match = metricsText.match(regex);
            return match ? parseFloat(match[1]) : 0;
        }

        function parseHistogramSum(metricsText, metricName) {
            const regex = new RegExp(`${metricName}_sum\\{[^}]*\\}\\s+(\\d+(?:\\.\\d+)?)`);
            const match = metricsText.match(regex);
            return match ? parseFloat(match[1]) : 0;
        }

        function parseHistogramCount(metricsText, metricName) {
            const regex = new RegExp(`${metricName}_count\\{[^}]*\\}\\s+(\\d+(?:\\.\\d+)?)`);
            const match = metricsText.match(regex);
            return match ? parseFloat(match[1]) : 0;
        }

        async function refreshMetrics() {
            const metricsText = await fetchMetrics();
            if (!metricsText) {
                document.getElementById('systemStatus').innerHTML = 
                    '<span class="status-indicator status-down"></span>连接失败';
                return;
            }

            // 解析指标
            const ordersSent = parseMetric(metricsText, 'kafka_order_sent_total');
            const ordersSentSuccess = parseMetric(metricsText, 'kafka_order_sent_success_total');
            const ordersSentFailure = parseMetric(metricsText, 'kafka_order_sent_failure_total');
            const ordersReceived = parseMetric(metricsText, 'kafka_order_received_total');
            const ordersProcessedSuccess = parseMetric(metricsText, 'kafka_order_processed_success_total');
            const ordersProcessedFailure = parseMetric(metricsText, 'kafka_order_processed_failure_total');
            const duplicateOrders = parseMetric(metricsText, 'kafka_order_duplicates');

            // 计算平均延迟
            const sendSum = parseHistogramSum(metricsText, 'kafka_order_send_duration_seconds');
            const sendCount = parseHistogramCount(metricsText, 'kafka_order_send_duration_seconds');
            const avgSendLatency = sendCount > 0 ? (sendSum / sendCount * 1000).toFixed(2) : 0;

            const processSum = parseHistogramSum(metricsText, 'kafka_order_processing_duration_seconds');
            const processCount = parseHistogramCount(metricsText, 'kafka_order_processing_duration_seconds');
            const avgProcessLatency = processCount > 0 ? (processSum / processCount * 1000).toFixed(2) : 0;

            // 更新页面
            document.getElementById('ordersSent').textContent = ordersSent;
            document.getElementById('ordersSentSuccess').textContent = ordersSentSuccess;
            document.getElementById('ordersSentFailure').textContent = ordersSentFailure;
            document.getElementById('ordersReceived').textContent = ordersReceived;
            document.getElementById('ordersProcessedSuccess').textContent = ordersProcessedSuccess;
            document.getElementById('ordersProcessedFailure').textContent = ordersProcessedFailure;
            document.getElementById('duplicateOrders').textContent = duplicateOrders;
            document.getElementById('avgSendLatency').textContent = avgSendLatency + ' ms';
            document.getElementById('avgProcessLatency').textContent = avgProcessLatency + ' ms';

            document.getElementById('lastUpdate').textContent = 
                '最后更新: ' + new Date().toLocaleTimeString();
        }

        // 页面加载时刷新指标
        refreshMetrics();

        // 每30秒自动刷新
        setInterval(refreshMetrics, 30000);
    </script>
</body>
</html>
