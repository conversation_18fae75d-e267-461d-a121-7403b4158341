#!/bin/bash

# Kafka 启动脚本
# 使用 KRaft 模式（无需 ZooKeeper）

KAFKA_HOME="$(pwd)/kafka"
KAFKA_CONFIG="$KAFKA_HOME/config/kraft/server.properties"

echo "正在启动 Kafka 服务器..."
echo "Kafka 主目录: $KAFKA_HOME"
echo "配置文件: $KAFKA_CONFIG"

# 检查 Kafka 是否已经在运行
if pgrep -f "kafka.Kafka" > /dev/null; then
    echo "Kafka 服务器已经在运行中！"
    exit 1
fi

# 启动 Kafka
nohup $KAFKA_HOME/bin/kafka-server-start.sh $KAFKA_CONFIG > kafka.log 2>&1 &

# 等待几秒钟让服务启动
sleep 5

# 检查是否启动成功
if pgrep -f "kafka.Kafka" > /dev/null; then
    echo "✅ Kafka 服务器启动成功！"
    echo "📝 日志文件: kafka.log"
    echo "🌐 客户端连接地址: localhost:9092"
    echo "🎛️  控制器地址: localhost:9093"
    echo ""
    echo "常用命令："
    echo "  查看主题: ./kafka/bin/kafka-topics.sh --list --bootstrap-server localhost:9092"
    echo "  创建主题: ./kafka/bin/kafka-topics.sh --create --topic <topic-name> --bootstrap-server localhost:9092"
    echo "  停止服务: ./stop-kafka.sh"
else
    echo "❌ Kafka 服务器启动失败！请检查 kafka.log 文件"
    exit 1
fi
