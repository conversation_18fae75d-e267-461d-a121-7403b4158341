# WSL环境专用配置
server:
  port: 8080

spring:
  application:
    name: kafka-order-service
  
  profiles:
    active: wsl
  
  kafka:
    # WSL环境Kafka配置
    bootstrap-servers: localhost:9092
    
    producer:
      # WSL环境优化配置
      batch-size: 16384          # 16KB (适中)
      linger-ms: 5               # 5ms延迟
      buffer-memory: 33554432    # 32MB缓冲区
      compression-type: lz4      # LZ4压缩
      
      # 可靠性配置
      acks: 1                    # 平衡性能和可靠性
      retries: 3
      enable-idempotence: true
      max-in-flight-requests-per-connection: 5
      
      # 超时配置
      request-timeout-ms: 30000
      delivery-timeout-ms: 120000
    
    consumer:
      group-id: order-service-wsl-group
      auto-offset-reset: earliest
      enable-auto-commit: false
      
      # WSL环境优化配置
      max-poll-records: 300      # 减少批处理大小
      max-poll-interval-ms: 300000
      session-timeout-ms: 30000
      heartbeat-interval-ms: 10000
      fetch-min-size: 1024       # 1KB最小拉取
      fetch-max-wait: 500
    
    listener:
      concurrency: 2             # WSL环境使用较少并发
      poll-timeout: 3000

# 应用配置
app:
  kafka:
    topics:
      order-events: order-events
      order-status-updates: order-status-updates
      order-dlq: order-dlq

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# WSL环境日志配置
logging:
  level:
    com.example.kafka.order: INFO
    org.springframework.kafka: WARN
    org.apache.kafka: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/kafka-order-service-wsl.log
    max-size: 50MB
    max-history: 10
