[2025-07-06 09:01:15,749] INFO [QuorumController id=1] Creating new QuorumController with clusterId zio5HxucRweCqKtfvhb1qg. (org.apache.kafka.controller.QuorumController)
[2025-07-06 09:01:15,753] INFO [QuorumController id=1] Becoming the active controller at epoch 1, next write offset 1. (org.apache.kafka.controller.QuorumController)
[2025-07-06 09:01:15,759] WARN [QuorumController id=1] Performing controller activation. The metadata log appears to be empty. Appending 1 bootstrap record(s) in metadata transaction at metadata.version 3.8-IV0 from bootstrap source 'the binary bootstrap metadata file: /tmp/kraft-combined-logs/bootstrap.checkpoint'. Setting the ZK migration state to NONE since this is a de-novo KRaft cluster. (org.apache.kafka.controller.QuorumController)
[2025-07-06 09:01:15,760] INFO [QuorumController id=1] Replayed BeginTransactionRecord(name='Bootstrap records') at offset 1. (org.apache.kafka.controller.OffsetControlManager)
[2025-07-06 09:01:15,760] INFO [QuorumController id=1] Replayed a FeatureLevelRecord setting metadata.version to 3.8-IV0 (org.apache.kafka.controller.FeatureControlManager)
[2025-07-06 09:01:15,761] INFO [QuorumController id=1] Replayed EndTransactionRecord() at offset 4. (org.apache.kafka.controller.OffsetControlManager)
[2025-07-06 09:01:15,985] INFO [QuorumController id=1] Replayed RegisterControllerRecord contaning ControllerRegistration(id=1, incarnationId=dDkvVYmZRpiis_Rf1Fcrrg, zkMigrationReady=false, listeners=[Endpoint(listenerName='CONTROLLER', securityProtocol=PLAINTEXT, host='LAPTOP-H9435UC9.', port=9093)], supportedFeatures={metadata.version: 1-20}). (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 09:01:16,070] WARN [QuorumController id=1] Broker 1 registered with feature metadata.version that is unknown to the controller (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 09:01:16,072] INFO [QuorumController id=1] Replayed initial RegisterBrokerRecord for broker 1: RegisterBrokerRecord(brokerId=1, isMigratingZkBroker=false, incarnationId=qbFTAsucR1iGd82hmTLRTw, brokerEpoch=6, endPoints=[BrokerEndpoint(name='PLAINTEXT', host='localhost', port=9092, securityProtocol=0)], features=[BrokerFeature(name='metadata.version', minSupportedVersion=1, maxSupportedVersion=20)], rack=null, fenced=true, inControlledShutdown=false, logDirs=[fByaQt4RR3XuGwEgMy431g]) (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 09:01:16,256] INFO [QuorumController id=1] The request from broker 1 to unfence has been granted because it has caught up with the offset of its register broker record 6. (org.apache.kafka.controller.BrokerHeartbeatManager)
[2025-07-06 09:01:16,263] INFO [QuorumController id=1] Replayed BrokerRegistrationChangeRecord modifying the registration for broker 1: BrokerRegistrationChangeRecord(brokerId=1, brokerEpoch=6, fenced=-1, inControlledShutdown=0, logDirs=[]) (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 09:01:38,491] INFO [QuorumController id=1] CreateTopics result(s): CreatableTopic(name='test-topic', numPartitions=1, replicationFactor=1, assignments=[], configs=[]): SUCCESS (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:01:38,491] INFO [QuorumController id=1] Replayed TopicRecord for topic test-topic with topic ID aebtOuNjRiGIlwAu7XzdUg. (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:01:38,492] INFO [QuorumController id=1] Replayed PartitionRecord for new partition test-topic-0 with topic ID aebtOuNjRiGIlwAu7XzdUg and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:04:27,907] INFO [QuorumController id=1] Creating new QuorumController with clusterId zio5HxucRweCqKtfvhb1qg. (org.apache.kafka.controller.QuorumController)
[2025-07-06 09:04:27,911] INFO [QuorumController id=1] Replayed BeginTransactionRecord(name='Bootstrap records') at offset 1. (org.apache.kafka.controller.OffsetControlManager)
[2025-07-06 09:04:27,912] INFO [QuorumController id=1] Replayed a FeatureLevelRecord setting metadata.version to 3.8-IV0 (org.apache.kafka.controller.FeatureControlManager)
[2025-07-06 09:04:27,912] INFO [QuorumController id=1] Replayed EndTransactionRecord() at offset 4. (org.apache.kafka.controller.OffsetControlManager)
[2025-07-06 09:04:27,913] INFO [QuorumController id=1] Replayed RegisterControllerRecord contaning ControllerRegistration(id=1, incarnationId=dDkvVYmZRpiis_Rf1Fcrrg, zkMigrationReady=false, listeners=[Endpoint(listenerName='CONTROLLER', securityProtocol=PLAINTEXT, host='LAPTOP-H9435UC9.', port=9093)], supportedFeatures={metadata.version: 1-20}). (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 09:04:27,914] INFO [QuorumController id=1] Replayed initial RegisterBrokerRecord for broker 1: RegisterBrokerRecord(brokerId=1, isMigratingZkBroker=false, incarnationId=qbFTAsucR1iGd82hmTLRTw, brokerEpoch=6, endPoints=[BrokerEndpoint(name='PLAINTEXT', host='localhost', port=9092, securityProtocol=0)], features=[BrokerFeature(name='metadata.version', minSupportedVersion=1, maxSupportedVersion=20)], rack=null, fenced=true, inControlledShutdown=false, logDirs=[fByaQt4RR3XuGwEgMy431g]) (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 09:04:27,914] INFO [QuorumController id=1] Replayed BrokerRegistrationChangeRecord modifying the registration for broker 1: BrokerRegistrationChangeRecord(brokerId=1, brokerEpoch=6, fenced=-1, inControlledShutdown=0, logDirs=[]) (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 09:04:27,915] INFO [QuorumController id=1] Replayed TopicRecord for topic test-topic with topic ID aebtOuNjRiGIlwAu7XzdUg. (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:04:27,915] INFO [QuorumController id=1] Replayed PartitionRecord for new partition test-topic-0 with topic ID aebtOuNjRiGIlwAu7XzdUg and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:04:27,923] INFO [QuorumController id=1] Becoming the active controller at epoch 2, next write offset 394. (org.apache.kafka.controller.QuorumController)
[2025-07-06 09:04:27,926] WARN [QuorumController id=1] Performing controller activation. Loaded ZK migration state of NONE. (org.apache.kafka.controller.QuorumController)
[2025-07-06 09:11:52,037] INFO [QuorumController id=1] CreateTopics result(s): CreatableTopic(name='kafka-test-topic', numPartitions=3, replicationFactor=1, assignments=[], configs=[]): SUCCESS (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:11:52,038] INFO [QuorumController id=1] Replayed TopicRecord for topic kafka-test-topic with topic ID fI5KC7RMSIyjr4ieFJyswA. (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:11:52,038] INFO [QuorumController id=1] Replayed PartitionRecord for new partition kafka-test-topic-0 with topic ID fI5KC7RMSIyjr4ieFJyswA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:11:52,038] INFO [QuorumController id=1] Replayed PartitionRecord for new partition kafka-test-topic-1 with topic ID fI5KC7RMSIyjr4ieFJyswA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:11:52,038] INFO [QuorumController id=1] Replayed PartitionRecord for new partition kafka-test-topic-2 with topic ID fI5KC7RMSIyjr4ieFJyswA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:28,343] INFO [QuorumController id=1] Replaying ProducerIdsRecord ProducerIdsRecord(brokerId=1, brokerEpoch=6, nextProducerId=1000) (org.apache.kafka.controller.ProducerIdControlManager)
[2025-07-06 09:12:38,204] INFO [QuorumController id=1] CreateTopics result(s): CreatableTopic(name='__consumer_offsets', numPartitions=50, replicationFactor=1, assignments=[], configs=[CreateableTopicConfig(name='compression.type', value='producer'), CreateableTopicConfig(name='cleanup.policy', value='compact'), CreateableTopicConfig(name='segment.bytes', value='104857600')]): SUCCESS (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,204] INFO [QuorumController id=1] Replayed TopicRecord for topic __consumer_offsets with topic ID KgEvLeQ_TKCKZrXBKyDpjA. (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,204] INFO [QuorumController id=1] Replayed ConfigRecord for ConfigResource(type=TOPIC, name='__consumer_offsets') which set configuration compression.type to producer (org.apache.kafka.controller.ConfigurationControlManager)
[2025-07-06 09:12:38,204] INFO [QuorumController id=1] Replayed ConfigRecord for ConfigResource(type=TOPIC, name='__consumer_offsets') which set configuration cleanup.policy to compact (org.apache.kafka.controller.ConfigurationControlManager)
[2025-07-06 09:12:38,204] INFO [QuorumController id=1] Replayed ConfigRecord for ConfigResource(type=TOPIC, name='__consumer_offsets') which set configuration segment.bytes to 104857600 (org.apache.kafka.controller.ConfigurationControlManager)
[2025-07-06 09:12:38,204] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-0 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,204] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-1 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,204] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-2 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,204] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-3 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,204] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-4 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,204] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-5 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,205] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-6 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,205] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-7 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,205] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-8 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,205] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-9 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,205] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-10 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,205] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-11 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,205] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-12 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,205] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-13 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,205] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-14 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,205] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-15 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,205] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-16 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,205] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-17 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,205] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-18 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,205] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-19 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,206] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-20 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,206] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-21 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,206] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-22 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,206] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-23 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,206] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-24 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,206] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-25 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,207] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-26 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,207] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-27 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,207] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-28 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,207] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-29 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,207] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-30 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,207] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-31 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,207] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-32 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,207] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-33 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,207] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-34 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,207] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-35 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,207] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-36 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,207] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-37 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,207] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-38 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,207] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-39 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,208] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-40 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,208] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-41 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,208] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-42 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,208] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-43 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,208] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-44 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,208] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-45 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,208] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-46 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,208] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-47 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,208] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-48 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:12:38,208] INFO [QuorumController id=1] Replayed PartitionRecord for new partition __consumer_offsets-49 with topic ID KgEvLeQ_TKCKZrXBKyDpjA and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
