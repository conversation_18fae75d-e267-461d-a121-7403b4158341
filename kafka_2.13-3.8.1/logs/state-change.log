[2025-07-06 09:01:38,530] INFO [Broker id=1] Transitioning 1 partition(s) to local leaders. (state.change.logger)
[2025-07-06 09:01:38,535] INFO [Broker id=1] Creating new partition test-topic-0 with topic id aebtOuNjRiGIlwAu7XzdUg. (state.change.logger)
[2025-07-06 09:01:38,563] INFO [Broker id=1] Leader test-topic-0 with topic id Some(aebtOuNjRiGIlwAu7XzdUg) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:11:52,066] INFO [Broker id=1] Transitioning 3 partition(s) to local leaders. (state.change.logger)
[2025-07-06 09:11:52,067] INFO [Broker id=1] Creating new partition kafka-test-topic-2 with topic id fI5KC7RMSIyjr4ieFJyswA. (state.change.logger)
[2025-07-06 09:11:52,073] INFO [Broker id=1] Leader kafka-test-topic-2 with topic id Some(fI5KC7RMSIyjr4ieFJyswA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:11:52,091] INFO [Broker id=1] Creating new partition kafka-test-topic-1 with topic id fI5KC7RMSIyjr4ieFJyswA. (state.change.logger)
[2025-07-06 09:11:52,094] INFO [Broker id=1] Leader kafka-test-topic-1 with topic id Some(fI5KC7RMSIyjr4ieFJyswA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:11:52,110] INFO [Broker id=1] Creating new partition kafka-test-topic-0 with topic id fI5KC7RMSIyjr4ieFJyswA. (state.change.logger)
[2025-07-06 09:11:52,114] INFO [Broker id=1] Leader kafka-test-topic-0 with topic id Some(fI5KC7RMSIyjr4ieFJyswA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,237] INFO [Broker id=1] Transitioning 50 partition(s) to local leaders. (state.change.logger)
[2025-07-06 09:12:38,237] INFO [Broker id=1] Creating new partition __consumer_offsets-13 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,242] INFO [Broker id=1] Leader __consumer_offsets-13 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,250] INFO [Broker id=1] Creating new partition __consumer_offsets-46 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,255] INFO [Broker id=1] Leader __consumer_offsets-46 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,266] INFO [Broker id=1] Creating new partition __consumer_offsets-9 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,271] INFO [Broker id=1] Leader __consumer_offsets-9 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,284] INFO [Broker id=1] Creating new partition __consumer_offsets-42 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,287] INFO [Broker id=1] Leader __consumer_offsets-42 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,300] INFO [Broker id=1] Creating new partition __consumer_offsets-21 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,304] INFO [Broker id=1] Leader __consumer_offsets-21 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,318] INFO [Broker id=1] Creating new partition __consumer_offsets-17 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,324] INFO [Broker id=1] Leader __consumer_offsets-17 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,337] INFO [Broker id=1] Creating new partition __consumer_offsets-30 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,341] INFO [Broker id=1] Leader __consumer_offsets-30 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,354] INFO [Broker id=1] Creating new partition __consumer_offsets-26 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,414] INFO [Broker id=1] Leader __consumer_offsets-26 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,423] INFO [Broker id=1] Creating new partition __consumer_offsets-5 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,427] INFO [Broker id=1] Leader __consumer_offsets-5 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,441] INFO [Broker id=1] Creating new partition __consumer_offsets-38 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,444] INFO [Broker id=1] Leader __consumer_offsets-38 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,457] INFO [Broker id=1] Creating new partition __consumer_offsets-1 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,462] INFO [Broker id=1] Leader __consumer_offsets-1 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,476] INFO [Broker id=1] Creating new partition __consumer_offsets-34 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,480] INFO [Broker id=1] Leader __consumer_offsets-34 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,493] INFO [Broker id=1] Creating new partition __consumer_offsets-16 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,497] INFO [Broker id=1] Leader __consumer_offsets-16 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,509] INFO [Broker id=1] Creating new partition __consumer_offsets-45 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,514] INFO [Broker id=1] Leader __consumer_offsets-45 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,526] INFO [Broker id=1] Creating new partition __consumer_offsets-12 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,531] INFO [Broker id=1] Leader __consumer_offsets-12 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,543] INFO [Broker id=1] Creating new partition __consumer_offsets-41 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,546] INFO [Broker id=1] Leader __consumer_offsets-41 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,558] INFO [Broker id=1] Creating new partition __consumer_offsets-24 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,562] INFO [Broker id=1] Leader __consumer_offsets-24 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,574] INFO [Broker id=1] Creating new partition __consumer_offsets-20 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,578] INFO [Broker id=1] Leader __consumer_offsets-20 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,585] INFO [Broker id=1] Creating new partition __consumer_offsets-49 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,590] INFO [Broker id=1] Leader __consumer_offsets-49 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,603] INFO [Broker id=1] Creating new partition __consumer_offsets-0 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,606] INFO [Broker id=1] Leader __consumer_offsets-0 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,617] INFO [Broker id=1] Creating new partition __consumer_offsets-29 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,624] INFO [Broker id=1] Leader __consumer_offsets-29 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,638] INFO [Broker id=1] Creating new partition __consumer_offsets-25 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,643] INFO [Broker id=1] Leader __consumer_offsets-25 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,660] INFO [Broker id=1] Creating new partition __consumer_offsets-8 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,663] INFO [Broker id=1] Leader __consumer_offsets-8 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,672] INFO [Broker id=1] Creating new partition __consumer_offsets-37 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,675] INFO [Broker id=1] Leader __consumer_offsets-37 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,688] INFO [Broker id=1] Creating new partition __consumer_offsets-4 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,691] INFO [Broker id=1] Leader __consumer_offsets-4 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,707] INFO [Broker id=1] Creating new partition __consumer_offsets-33 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,710] INFO [Broker id=1] Leader __consumer_offsets-33 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,716] INFO [Broker id=1] Creating new partition __consumer_offsets-15 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,719] INFO [Broker id=1] Leader __consumer_offsets-15 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,726] INFO [Broker id=1] Creating new partition __consumer_offsets-48 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,729] INFO [Broker id=1] Leader __consumer_offsets-48 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,741] INFO [Broker id=1] Creating new partition __consumer_offsets-11 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,744] INFO [Broker id=1] Leader __consumer_offsets-11 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,764] INFO [Broker id=1] Creating new partition __consumer_offsets-44 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,767] INFO [Broker id=1] Leader __consumer_offsets-44 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,785] INFO [Broker id=1] Creating new partition __consumer_offsets-23 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,790] INFO [Broker id=1] Leader __consumer_offsets-23 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,807] INFO [Broker id=1] Creating new partition __consumer_offsets-19 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,810] INFO [Broker id=1] Leader __consumer_offsets-19 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,828] INFO [Broker id=1] Creating new partition __consumer_offsets-32 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,833] INFO [Broker id=1] Leader __consumer_offsets-32 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,853] INFO [Broker id=1] Creating new partition __consumer_offsets-28 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,857] INFO [Broker id=1] Leader __consumer_offsets-28 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,871] INFO [Broker id=1] Creating new partition __consumer_offsets-7 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,931] INFO [Broker id=1] Leader __consumer_offsets-7 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,953] INFO [Broker id=1] Creating new partition __consumer_offsets-40 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,957] INFO [Broker id=1] Leader __consumer_offsets-40 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,974] INFO [Broker id=1] Creating new partition __consumer_offsets-3 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,977] INFO [Broker id=1] Leader __consumer_offsets-3 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:38,989] INFO [Broker id=1] Creating new partition __consumer_offsets-36 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:38,992] INFO [Broker id=1] Leader __consumer_offsets-36 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:39,006] INFO [Broker id=1] Creating new partition __consumer_offsets-47 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:39,010] INFO [Broker id=1] Leader __consumer_offsets-47 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:39,021] INFO [Broker id=1] Creating new partition __consumer_offsets-14 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:39,024] INFO [Broker id=1] Leader __consumer_offsets-14 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:39,036] INFO [Broker id=1] Creating new partition __consumer_offsets-43 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:39,040] INFO [Broker id=1] Leader __consumer_offsets-43 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:39,053] INFO [Broker id=1] Creating new partition __consumer_offsets-10 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:39,056] INFO [Broker id=1] Leader __consumer_offsets-10 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:39,070] INFO [Broker id=1] Creating new partition __consumer_offsets-22 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:39,073] INFO [Broker id=1] Leader __consumer_offsets-22 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:39,089] INFO [Broker id=1] Creating new partition __consumer_offsets-18 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:39,095] INFO [Broker id=1] Leader __consumer_offsets-18 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:39,108] INFO [Broker id=1] Creating new partition __consumer_offsets-31 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:39,113] INFO [Broker id=1] Leader __consumer_offsets-31 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:39,135] INFO [Broker id=1] Creating new partition __consumer_offsets-27 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:39,138] INFO [Broker id=1] Leader __consumer_offsets-27 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:39,154] INFO [Broker id=1] Creating new partition __consumer_offsets-39 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:39,157] INFO [Broker id=1] Leader __consumer_offsets-39 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:39,173] INFO [Broker id=1] Creating new partition __consumer_offsets-6 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:39,177] INFO [Broker id=1] Leader __consumer_offsets-6 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:39,191] INFO [Broker id=1] Creating new partition __consumer_offsets-35 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:39,194] INFO [Broker id=1] Leader __consumer_offsets-35 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
[2025-07-06 09:12:39,210] INFO [Broker id=1] Creating new partition __consumer_offsets-2 with topic id KgEvLeQ_TKCKZrXBKyDpjA. (state.change.logger)
[2025-07-06 09:12:39,213] INFO [Broker id=1] Leader __consumer_offsets-2 with topic id Some(KgEvLeQ_TKCKZrXBKyDpjA) starts at leader epoch 0 from offset 0 with partition epoch 0, high watermark 0, ISR [1], adding replicas [] and removing replicas [] . Previous leader None and previous leader epoch was -1. (state.change.logger)
