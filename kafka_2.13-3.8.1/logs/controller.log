[2025-07-06 09:01:15,749] INFO [QuorumController id=1] Creating new QuorumController with clusterId zio5HxucRweCqKtfvhb1qg. (org.apache.kafka.controller.QuorumController)
[2025-07-06 09:01:15,753] INFO [QuorumController id=1] Becoming the active controller at epoch 1, next write offset 1. (org.apache.kafka.controller.QuorumController)
[2025-07-06 09:01:15,759] WARN [QuorumController id=1] Performing controller activation. The metadata log appears to be empty. Appending 1 bootstrap record(s) in metadata transaction at metadata.version 3.8-IV0 from bootstrap source 'the binary bootstrap metadata file: /tmp/kraft-combined-logs/bootstrap.checkpoint'. Setting the ZK migration state to NONE since this is a de-novo KRaft cluster. (org.apache.kafka.controller.QuorumController)
[2025-07-06 09:01:15,760] INFO [QuorumController id=1] Replayed BeginTransactionRecord(name='Bootstrap records') at offset 1. (org.apache.kafka.controller.OffsetControlManager)
[2025-07-06 09:01:15,760] INFO [QuorumController id=1] Replayed a FeatureLevelRecord setting metadata.version to 3.8-IV0 (org.apache.kafka.controller.FeatureControlManager)
[2025-07-06 09:01:15,761] INFO [QuorumController id=1] Replayed EndTransactionRecord() at offset 4. (org.apache.kafka.controller.OffsetControlManager)
[2025-07-06 09:01:15,985] INFO [QuorumController id=1] Replayed RegisterControllerRecord contaning ControllerRegistration(id=1, incarnationId=dDkvVYmZRpiis_Rf1Fcrrg, zkMigrationReady=false, listeners=[Endpoint(listenerName='CONTROLLER', securityProtocol=PLAINTEXT, host='LAPTOP-H9435UC9.', port=9093)], supportedFeatures={metadata.version: 1-20}). (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 09:01:16,070] WARN [QuorumController id=1] Broker 1 registered with feature metadata.version that is unknown to the controller (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 09:01:16,072] INFO [QuorumController id=1] Replayed initial RegisterBrokerRecord for broker 1: RegisterBrokerRecord(brokerId=1, isMigratingZkBroker=false, incarnationId=qbFTAsucR1iGd82hmTLRTw, brokerEpoch=6, endPoints=[BrokerEndpoint(name='PLAINTEXT', host='localhost', port=9092, securityProtocol=0)], features=[BrokerFeature(name='metadata.version', minSupportedVersion=1, maxSupportedVersion=20)], rack=null, fenced=true, inControlledShutdown=false, logDirs=[fByaQt4RR3XuGwEgMy431g]) (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 09:01:16,256] INFO [QuorumController id=1] The request from broker 1 to unfence has been granted because it has caught up with the offset of its register broker record 6. (org.apache.kafka.controller.BrokerHeartbeatManager)
[2025-07-06 09:01:16,263] INFO [QuorumController id=1] Replayed BrokerRegistrationChangeRecord modifying the registration for broker 1: BrokerRegistrationChangeRecord(brokerId=1, brokerEpoch=6, fenced=-1, inControlledShutdown=0, logDirs=[]) (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 09:01:38,491] INFO [QuorumController id=1] CreateTopics result(s): CreatableTopic(name='test-topic', numPartitions=1, replicationFactor=1, assignments=[], configs=[]): SUCCESS (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:01:38,491] INFO [QuorumController id=1] Replayed TopicRecord for topic test-topic with topic ID aebtOuNjRiGIlwAu7XzdUg. (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:01:38,492] INFO [QuorumController id=1] Replayed PartitionRecord for new partition test-topic-0 with topic ID aebtOuNjRiGIlwAu7XzdUg and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:04:27,907] INFO [QuorumController id=1] Creating new QuorumController with clusterId zio5HxucRweCqKtfvhb1qg. (org.apache.kafka.controller.QuorumController)
[2025-07-06 09:04:27,911] INFO [QuorumController id=1] Replayed BeginTransactionRecord(name='Bootstrap records') at offset 1. (org.apache.kafka.controller.OffsetControlManager)
[2025-07-06 09:04:27,912] INFO [QuorumController id=1] Replayed a FeatureLevelRecord setting metadata.version to 3.8-IV0 (org.apache.kafka.controller.FeatureControlManager)
[2025-07-06 09:04:27,912] INFO [QuorumController id=1] Replayed EndTransactionRecord() at offset 4. (org.apache.kafka.controller.OffsetControlManager)
[2025-07-06 09:04:27,913] INFO [QuorumController id=1] Replayed RegisterControllerRecord contaning ControllerRegistration(id=1, incarnationId=dDkvVYmZRpiis_Rf1Fcrrg, zkMigrationReady=false, listeners=[Endpoint(listenerName='CONTROLLER', securityProtocol=PLAINTEXT, host='LAPTOP-H9435UC9.', port=9093)], supportedFeatures={metadata.version: 1-20}). (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 09:04:27,914] INFO [QuorumController id=1] Replayed initial RegisterBrokerRecord for broker 1: RegisterBrokerRecord(brokerId=1, isMigratingZkBroker=false, incarnationId=qbFTAsucR1iGd82hmTLRTw, brokerEpoch=6, endPoints=[BrokerEndpoint(name='PLAINTEXT', host='localhost', port=9092, securityProtocol=0)], features=[BrokerFeature(name='metadata.version', minSupportedVersion=1, maxSupportedVersion=20)], rack=null, fenced=true, inControlledShutdown=false, logDirs=[fByaQt4RR3XuGwEgMy431g]) (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 09:04:27,914] INFO [QuorumController id=1] Replayed BrokerRegistrationChangeRecord modifying the registration for broker 1: BrokerRegistrationChangeRecord(brokerId=1, brokerEpoch=6, fenced=-1, inControlledShutdown=0, logDirs=[]) (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 09:04:27,915] INFO [QuorumController id=1] Replayed TopicRecord for topic test-topic with topic ID aebtOuNjRiGIlwAu7XzdUg. (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:04:27,915] INFO [QuorumController id=1] Replayed PartitionRecord for new partition test-topic-0 with topic ID aebtOuNjRiGIlwAu7XzdUg and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 09:04:27,923] INFO [QuorumController id=1] Becoming the active controller at epoch 2, next write offset 394. (org.apache.kafka.controller.QuorumController)
[2025-07-06 09:04:27,926] WARN [QuorumController id=1] Performing controller activation. Loaded ZK migration state of NONE. (org.apache.kafka.controller.QuorumController)
