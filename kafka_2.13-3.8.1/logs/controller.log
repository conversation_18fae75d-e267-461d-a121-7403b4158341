[2025-07-06 10:21:26,865] INFO [QuorumController id=1] CreateTopics result(s): CreatableTopic(name='order-status-updates', numPartitions=1, replicationFactor=1, assignments=[], configs=[]): SUCCESS (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,866] INFO [QuorumController id=1] Replayed TopicRecord for topic order-status-updates with topic ID w42xGAjzQzGKXrytSz0tyQ. (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,866] INFO [QuorumController id=1] Replayed PartitionRecord for new partition order-status-updates-0 with topic ID w42xGAjzQzGKXrytSz0tyQ and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,899] INFO [QuorumController id=1] CreateTopics result(s): CreatableTopic(name='order-events', numPartitions=1, replicationFactor=1, assignments=[], configs=[]): SUCCESS (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,899] INFO [QuorumController id=1] Replayed TopicRecord for topic order-events with topic ID XgeA4B8mSmO1b0LIxwl8Tg. (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,899] INFO [QuorumController id=1] Replayed PartitionRecord for new partition order-events-0 with topic ID XgeA4B8mSmO1b0LIxwl8Tg and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,926] INFO [QuorumController id=1] CreateTopics result(s): CreatableTopic(name='order-dlq', numPartitions=1, replicationFactor=1, assignments=[], configs=[]): SUCCESS (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,926] INFO [QuorumController id=1] Replayed TopicRecord for topic order-dlq with topic ID ZdoPg4vpR9WT8PIEsk1aHg. (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,926] INFO [QuorumController id=1] Replayed PartitionRecord for new partition order-dlq-0 with topic ID ZdoPg4vpR9WT8PIEsk1aHg and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
