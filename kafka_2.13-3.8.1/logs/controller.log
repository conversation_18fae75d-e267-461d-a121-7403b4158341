[2025-07-06 10:21:26,865] INFO [QuorumController id=1] CreateTopics result(s): CreatableTopic(name='order-status-updates', numPartitions=1, replicationFactor=1, assignments=[], configs=[]): SUCCESS (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,866] INFO [QuorumController id=1] Replayed TopicRecord for topic order-status-updates with topic ID w42xGAjzQzGKXrytSz0tyQ. (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,866] INFO [QuorumController id=1] Replayed PartitionRecord for new partition order-status-updates-0 with topic ID w42xGAjzQzGKXrytSz0tyQ and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,899] INFO [QuorumController id=1] CreateTopics result(s): CreatableTopic(name='order-events', numPartitions=1, replicationFactor=1, assignments=[], configs=[]): SUCCESS (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,899] INFO [QuorumController id=1] Replayed TopicRecord for topic order-events with topic ID XgeA4B8mSmO1b0LIxwl8Tg. (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,899] INFO [QuorumController id=1] Replayed PartitionRecord for new partition order-events-0 with topic ID XgeA4B8mSmO1b0LIxwl8Tg and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,926] INFO [QuorumController id=1] CreateTopics result(s): CreatableTopic(name='order-dlq', numPartitions=1, replicationFactor=1, assignments=[], configs=[]): SUCCESS (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,926] INFO [QuorumController id=1] Replayed TopicRecord for topic order-dlq with topic ID ZdoPg4vpR9WT8PIEsk1aHg. (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:21:26,926] INFO [QuorumController id=1] Replayed PartitionRecord for new partition order-dlq-0 with topic ID ZdoPg4vpR9WT8PIEsk1aHg and PartitionRegistration(replicas=[1], directories=[fByaQt4RR3XuGwEgMy431g], isr=[1], removingReplicas=[], addingReplicas=[], elr=[], lastKnownElr=[], leader=1, leaderRecoveryState=RECOVERED, leaderEpoch=0, partitionEpoch=0). (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:29:10,840] INFO [QuorumController id=1] Unfenced broker 1 has requested and been granted a controlled shutdown. (org.apache.kafka.controller.BrokerHeartbeatManager)
[2025-07-06 10:29:10,853] INFO [QuorumController id=1] enterControlledShutdown[1]: changing 57 partition(s) (org.apache.kafka.controller.ReplicationControlManager)
[2025-07-06 10:29:10,854] INFO [QuorumController id=1] Replayed BrokerRegistrationChangeRecord modifying the registration for broker 1: BrokerRegistrationChangeRecord(brokerId=1, brokerEpoch=6, fenced=0, inControlledShutdown=1, logDirs=[]) (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 10:29:10,938] INFO [QuorumController id=1] The request from broker 1 to shut down has been granted since the lowest active offset 9223372036854775807 is now greater than the broker's controlled shutdown offset 8548. (org.apache.kafka.controller.BrokerHeartbeatManager)
[2025-07-06 10:29:10,946] INFO [QuorumController id=1] Replayed BrokerRegistrationChangeRecord modifying the registration for broker 1: BrokerRegistrationChangeRecord(brokerId=1, brokerEpoch=6, fenced=1, inControlledShutdown=0, logDirs=[]) (org.apache.kafka.controller.ClusterControlManager)
[2025-07-06 10:29:11,868] INFO [QuorumController id=1] writeNoOpRecord: event unable to start processing because of RejectedExecutionException (treated as TimeoutException). Exception message: The event queue is shutting down (org.apache.kafka.controller.QuorumController)
[2025-07-06 10:29:11,868] INFO [QuorumController id=1] maybeBalancePartitionLeaders: event unable to start processing because of RejectedExecutionException (treated as TimeoutException). Exception message: The event queue is shutting down (org.apache.kafka.controller.QuorumController)
