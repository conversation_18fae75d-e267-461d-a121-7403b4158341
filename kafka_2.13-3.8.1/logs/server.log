[2025-07-06 10:19:00,904] INFO [SnapshotGenerator id=1] Creating new KRaft snapshot file snapshot 00000000000000007266-0000000001 because we have waited at least 60 minute(s). (org.apache.kafka.image.publisher.SnapshotGenerator)
[2025-07-06 10:19:00,978] INFO [SnapshotEmitter id=1] Successfully wrote snapshot 00000000000000007266-0000000001 (org.apache.kafka.image.publisher.SnapshotEmitter)
[2025-07-06 10:21:26,846] INFO Sent auto-creation request for Set(order-dlq) to the active controller. (kafka.server.DefaultAutoTopicCreationManager)
[2025-07-06 10:21:26,846] INFO Sent auto-creation request for Set(order-status-updates) to the active controller. (kafka.server.DefaultAutoTopicCreationManager)
[2025-07-06 10:21:26,846] INFO Sent auto-creation request for Set(order-events) to the active controller. (kafka.server.DefaultAutoTopicCreationManager)
[2025-07-06 10:21:26,880] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group-dlq in Empty state. Created a new member id consumer-order-service-group-dlq-7-180701d0-857a-4b05-84a8-56fa76795057 and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,880] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group in Empty state. Created a new member id consumer-order-service-group-4-81f76066-c6ef-4675-9bd7-d4397bdc5bce and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,882] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group-dlq in Empty state. Created a new member id consumer-order-service-group-dlq-9-ec072fe1-07c8-4747-a522-43e51fe2b45d and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,882] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group in Empty state. Created a new member id consumer-order-service-group-5-9f6da1f4-7f5a-4f25-8735-9bd7374ccdf6 and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,883] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group in Empty state. Created a new member id consumer-order-service-group-1-a01e005a-b16e-49b0-883f-b8e030184fbc and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,883] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group in Empty state. Created a new member id consumer-order-service-group-2-0c0f8caa-0551-4143-85cc-104e301c099f and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,883] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group in Empty state. Created a new member id consumer-order-service-group-3-59e749ed-e975-424a-918a-344d32002c31 and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,884] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group in Empty state. Created a new member id consumer-order-service-group-6-72681ea4-cf76-4a24-bf51-2ae07b83f4a0 and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,885] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group-dlq in Empty state. Created a new member id consumer-order-service-group-dlq-8-1bcd61e5-ff2f-483b-81a3-b5de0c421641 and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,888] INFO [GroupCoordinator 1]: Preparing to rebalance group order-service-group in state PreparingRebalance with old generation 0 (__consumer_offsets-32) (reason: Adding new member consumer-order-service-group-5-9f6da1f4-7f5a-4f25-8735-9bd7374ccdf6 with group instance id None; client reason: not provided) (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,888] INFO [GroupCoordinator 1]: Preparing to rebalance group order-service-group-dlq in state PreparingRebalance with old generation 0 (__consumer_offsets-0) (reason: Adding new member consumer-order-service-group-dlq-9-ec072fe1-07c8-4747-a522-43e51fe2b45d with group instance id None; client reason: not provided) (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,896] INFO [ReplicaFetcherManager on broker 1] Removed fetcher for partitions Set(order-status-updates-0) (kafka.server.ReplicaFetcherManager)
[2025-07-06 10:21:26,903] INFO [LogLoader partition=order-status-updates-0, dir=/tmp/kraft-combined-logs] Loading producer state till offset 0 with message format version 2 (kafka.log.UnifiedLog$)
[2025-07-06 10:21:26,904] INFO Created log for partition order-status-updates-0 in /tmp/kraft-combined-logs/order-status-updates-0 with properties {} (kafka.log.LogManager)
[2025-07-06 10:21:26,905] INFO [Partition order-status-updates-0 broker=1] No checkpointed highwatermark is found for partition order-status-updates-0 (kafka.cluster.Partition)
[2025-07-06 10:21:26,905] INFO [Partition order-status-updates-0 broker=1] Log loaded for partition order-status-updates-0 with initial high watermark 0 (kafka.cluster.Partition)
[2025-07-06 10:21:26,924] INFO [ReplicaFetcherManager on broker 1] Removed fetcher for partitions Set(order-events-0) (kafka.server.ReplicaFetcherManager)
[2025-07-06 10:21:26,926] INFO [LogLoader partition=order-events-0, dir=/tmp/kraft-combined-logs] Loading producer state till offset 0 with message format version 2 (kafka.log.UnifiedLog$)
[2025-07-06 10:21:26,927] INFO Created log for partition order-events-0 in /tmp/kraft-combined-logs/order-events-0 with properties {} (kafka.log.LogManager)
[2025-07-06 10:21:26,927] INFO [Partition order-events-0 broker=1] No checkpointed highwatermark is found for partition order-events-0 (kafka.cluster.Partition)
[2025-07-06 10:21:26,927] INFO [Partition order-events-0 broker=1] Log loaded for partition order-events-0 with initial high watermark 0 (kafka.cluster.Partition)
[2025-07-06 10:21:26,954] INFO [ReplicaFetcherManager on broker 1] Removed fetcher for partitions Set(order-dlq-0) (kafka.server.ReplicaFetcherManager)
[2025-07-06 10:21:26,957] INFO [LogLoader partition=order-dlq-0, dir=/tmp/kraft-combined-logs] Loading producer state till offset 0 with message format version 2 (kafka.log.UnifiedLog$)
[2025-07-06 10:21:26,957] INFO Created log for partition order-dlq-0 in /tmp/kraft-combined-logs/order-dlq-0 with properties {} (kafka.log.LogManager)
[2025-07-06 10:21:26,960] INFO [Partition order-dlq-0 broker=1] No checkpointed highwatermark is found for partition order-dlq-0 (kafka.cluster.Partition)
[2025-07-06 10:21:26,961] INFO [Partition order-dlq-0 broker=1] Log loaded for partition order-dlq-0 with initial high watermark 0 (kafka.cluster.Partition)
[2025-07-06 10:21:32,893] INFO [GroupCoordinator 1]: Stabilized group order-service-group-dlq generation 1 (__consumer_offsets-0) with 3 members (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:32,907] INFO [GroupCoordinator 1]: Stabilized group order-service-group generation 1 (__consumer_offsets-32) with 6 members (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:32,927] INFO [GroupCoordinator 1]: Assignment received from leader consumer-order-service-group-5-9f6da1f4-7f5a-4f25-8735-9bd7374ccdf6 for group order-service-group for generation 1. The group has 6 members, 0 of which are static. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:32,927] INFO [GroupCoordinator 1]: Assignment received from leader consumer-order-service-group-dlq-9-ec072fe1-07c8-4747-a522-43e51fe2b45d for group order-service-group-dlq for generation 1. The group has 3 members, 0 of which are static. (kafka.coordinator.group.GroupCoordinator)
