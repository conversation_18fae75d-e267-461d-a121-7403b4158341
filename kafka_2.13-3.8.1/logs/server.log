[2025-07-06 10:19:00,904] INFO [SnapshotGenerator id=1] Creating new KRaft snapshot file snapshot 00000000000000007266-0000000001 because we have waited at least 60 minute(s). (org.apache.kafka.image.publisher.SnapshotGenerator)
[2025-07-06 10:19:00,978] INFO [SnapshotEmitter id=1] Successfully wrote snapshot 00000000000000007266-0000000001 (org.apache.kafka.image.publisher.SnapshotEmitter)
[2025-07-06 10:21:26,846] INFO Sent auto-creation request for Set(order-dlq) to the active controller. (kafka.server.DefaultAutoTopicCreationManager)
[2025-07-06 10:21:26,846] INFO Sent auto-creation request for Set(order-status-updates) to the active controller. (kafka.server.DefaultAutoTopicCreationManager)
[2025-07-06 10:21:26,846] INFO Sent auto-creation request for Set(order-events) to the active controller. (kafka.server.DefaultAutoTopicCreationManager)
[2025-07-06 10:21:26,880] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group-dlq in Empty state. Created a new member id consumer-order-service-group-dlq-7-180701d0-857a-4b05-84a8-56fa76795057 and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,880] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group in Empty state. Created a new member id consumer-order-service-group-4-81f76066-c6ef-4675-9bd7-d4397bdc5bce and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,882] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group-dlq in Empty state. Created a new member id consumer-order-service-group-dlq-9-ec072fe1-07c8-4747-a522-43e51fe2b45d and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,882] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group in Empty state. Created a new member id consumer-order-service-group-5-9f6da1f4-7f5a-4f25-8735-9bd7374ccdf6 and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,883] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group in Empty state. Created a new member id consumer-order-service-group-1-a01e005a-b16e-49b0-883f-b8e030184fbc and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,883] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group in Empty state. Created a new member id consumer-order-service-group-2-0c0f8caa-0551-4143-85cc-104e301c099f and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,883] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group in Empty state. Created a new member id consumer-order-service-group-3-59e749ed-e975-424a-918a-344d32002c31 and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,884] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group in Empty state. Created a new member id consumer-order-service-group-6-72681ea4-cf76-4a24-bf51-2ae07b83f4a0 and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,885] INFO [GroupCoordinator 1]: Dynamic member with unknown member id joins group order-service-group-dlq in Empty state. Created a new member id consumer-order-service-group-dlq-8-1bcd61e5-ff2f-483b-81a3-b5de0c421641 and request the member to rejoin with this id. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,888] INFO [GroupCoordinator 1]: Preparing to rebalance group order-service-group in state PreparingRebalance with old generation 0 (__consumer_offsets-32) (reason: Adding new member consumer-order-service-group-5-9f6da1f4-7f5a-4f25-8735-9bd7374ccdf6 with group instance id None; client reason: not provided) (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,888] INFO [GroupCoordinator 1]: Preparing to rebalance group order-service-group-dlq in state PreparingRebalance with old generation 0 (__consumer_offsets-0) (reason: Adding new member consumer-order-service-group-dlq-9-ec072fe1-07c8-4747-a522-43e51fe2b45d with group instance id None; client reason: not provided) (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:26,896] INFO [ReplicaFetcherManager on broker 1] Removed fetcher for partitions Set(order-status-updates-0) (kafka.server.ReplicaFetcherManager)
[2025-07-06 10:21:26,903] INFO [LogLoader partition=order-status-updates-0, dir=/tmp/kraft-combined-logs] Loading producer state till offset 0 with message format version 2 (kafka.log.UnifiedLog$)
[2025-07-06 10:21:26,904] INFO Created log for partition order-status-updates-0 in /tmp/kraft-combined-logs/order-status-updates-0 with properties {} (kafka.log.LogManager)
[2025-07-06 10:21:26,905] INFO [Partition order-status-updates-0 broker=1] No checkpointed highwatermark is found for partition order-status-updates-0 (kafka.cluster.Partition)
[2025-07-06 10:21:26,905] INFO [Partition order-status-updates-0 broker=1] Log loaded for partition order-status-updates-0 with initial high watermark 0 (kafka.cluster.Partition)
[2025-07-06 10:21:26,924] INFO [ReplicaFetcherManager on broker 1] Removed fetcher for partitions Set(order-events-0) (kafka.server.ReplicaFetcherManager)
[2025-07-06 10:21:26,926] INFO [LogLoader partition=order-events-0, dir=/tmp/kraft-combined-logs] Loading producer state till offset 0 with message format version 2 (kafka.log.UnifiedLog$)
[2025-07-06 10:21:26,927] INFO Created log for partition order-events-0 in /tmp/kraft-combined-logs/order-events-0 with properties {} (kafka.log.LogManager)
[2025-07-06 10:21:26,927] INFO [Partition order-events-0 broker=1] No checkpointed highwatermark is found for partition order-events-0 (kafka.cluster.Partition)
[2025-07-06 10:21:26,927] INFO [Partition order-events-0 broker=1] Log loaded for partition order-events-0 with initial high watermark 0 (kafka.cluster.Partition)
[2025-07-06 10:21:26,954] INFO [ReplicaFetcherManager on broker 1] Removed fetcher for partitions Set(order-dlq-0) (kafka.server.ReplicaFetcherManager)
[2025-07-06 10:21:26,957] INFO [LogLoader partition=order-dlq-0, dir=/tmp/kraft-combined-logs] Loading producer state till offset 0 with message format version 2 (kafka.log.UnifiedLog$)
[2025-07-06 10:21:26,957] INFO Created log for partition order-dlq-0 in /tmp/kraft-combined-logs/order-dlq-0 with properties {} (kafka.log.LogManager)
[2025-07-06 10:21:26,960] INFO [Partition order-dlq-0 broker=1] No checkpointed highwatermark is found for partition order-dlq-0 (kafka.cluster.Partition)
[2025-07-06 10:21:26,961] INFO [Partition order-dlq-0 broker=1] Log loaded for partition order-dlq-0 with initial high watermark 0 (kafka.cluster.Partition)
[2025-07-06 10:21:32,893] INFO [GroupCoordinator 1]: Stabilized group order-service-group-dlq generation 1 (__consumer_offsets-0) with 3 members (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:32,907] INFO [GroupCoordinator 1]: Stabilized group order-service-group generation 1 (__consumer_offsets-32) with 6 members (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:32,927] INFO [GroupCoordinator 1]: Assignment received from leader consumer-order-service-group-5-9f6da1f4-7f5a-4f25-8735-9bd7374ccdf6 for group order-service-group for generation 1. The group has 6 members, 0 of which are static. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:21:32,927] INFO [GroupCoordinator 1]: Assignment received from leader consumer-order-service-group-dlq-9-ec072fe1-07c8-4747-a522-43e51fe2b45d for group order-service-group-dlq for generation 1. The group has 3 members, 0 of which are static. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,831] INFO Terminating process due to signal SIGHUP (org.apache.kafka.common.utils.LoggingSignalHandler)
[2025-07-06 10:29:10,836] INFO [BrokerServer id=1] Transition from STARTED to SHUTTING_DOWN (kafka.server.BrokerServer)
[2025-07-06 10:29:10,837] INFO [BrokerServer id=1] shutting down (kafka.server.BrokerServer)
[2025-07-06 10:29:10,839] INFO [BrokerLifecycleManager id=1] Beginning controlled shutdown. (kafka.server.BrokerLifecycleManager)
[2025-07-06 10:29:10,858] INFO [GroupCoordinator 1]: Preparing to rebalance group order-service-group-dlq in state PreparingRebalance with old generation 1 (__consumer_offsets-0) (reason: Removing member consumer-order-service-group-dlq-9-ec072fe1-07c8-4747-a522-43e51fe2b45d on LeaveGroup; client reason: not provided) (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,858] INFO [GroupCoordinator 1]: Preparing to rebalance group order-service-group in state PreparingRebalance with old generation 1 (__consumer_offsets-32) (reason: Removing member consumer-order-service-group-3-59e749ed-e975-424a-918a-344d32002c31 on LeaveGroup; client reason: not provided) (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,859] INFO [GroupCoordinator 1]: Member MemberMetadata(memberId=consumer-order-service-group-dlq-9-ec072fe1-07c8-4747-a522-43e51fe2b45d, groupInstanceId=None, clientId=consumer-order-service-group-dlq-9, clientHost=/127.0.0.1, sessionTimeoutMs=30000, rebalanceTimeoutMs=300000, supportedProtocols=List(range)) has left group order-service-group-dlq through explicit `LeaveGroup`; client reason: not provided (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,859] INFO [GroupCoordinator 1]: Member MemberMetadata(memberId=consumer-order-service-group-3-59e749ed-e975-424a-918a-344d32002c31, groupInstanceId=None, clientId=consumer-order-service-group-3, clientHost=/127.0.0.1, sessionTimeoutMs=30000, rebalanceTimeoutMs=300000, supportedProtocols=List(range)) has left group order-service-group through explicit `LeaveGroup`; client reason: not provided (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,860] INFO [GroupCoordinator 1]: Member MemberMetadata(memberId=consumer-order-service-group-dlq-8-1bcd61e5-ff2f-483b-81a3-b5de0c421641, groupInstanceId=None, clientId=consumer-order-service-group-dlq-8, clientHost=/127.0.0.1, sessionTimeoutMs=30000, rebalanceTimeoutMs=300000, supportedProtocols=List(range)) has left group order-service-group-dlq through explicit `LeaveGroup`; client reason: not provided (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,860] INFO [GroupCoordinator 1]: Member MemberMetadata(memberId=consumer-order-service-group-2-0c0f8caa-0551-4143-85cc-104e301c099f, groupInstanceId=None, clientId=consumer-order-service-group-2, clientHost=/127.0.0.1, sessionTimeoutMs=30000, rebalanceTimeoutMs=300000, supportedProtocols=List(range)) has left group order-service-group through explicit `LeaveGroup`; client reason: not provided (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,860] INFO [GroupCoordinator 1]: Member MemberMetadata(memberId=consumer-order-service-group-5-9f6da1f4-7f5a-4f25-8735-9bd7374ccdf6, groupInstanceId=None, clientId=consumer-order-service-group-5, clientHost=/127.0.0.1, sessionTimeoutMs=30000, rebalanceTimeoutMs=300000, supportedProtocols=List(range)) has left group order-service-group through explicit `LeaveGroup`; client reason: not provided (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,861] INFO [GroupCoordinator 1]: Member MemberMetadata(memberId=consumer-order-service-group-6-72681ea4-cf76-4a24-bf51-2ae07b83f4a0, groupInstanceId=None, clientId=consumer-order-service-group-6, clientHost=/127.0.0.1, sessionTimeoutMs=30000, rebalanceTimeoutMs=300000, supportedProtocols=List(range)) has left group order-service-group through explicit `LeaveGroup`; client reason: not provided (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,862] INFO [GroupCoordinator 1]: Member MemberMetadata(memberId=consumer-order-service-group-1-a01e005a-b16e-49b0-883f-b8e030184fbc, groupInstanceId=None, clientId=consumer-order-service-group-1, clientHost=/127.0.0.1, sessionTimeoutMs=30000, rebalanceTimeoutMs=300000, supportedProtocols=List(range)) has left group order-service-group through explicit `LeaveGroup`; client reason: not provided (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,863] INFO [GroupCoordinator 1]: Group order-service-group with generation 2 is now empty (__consumer_offsets-32) (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,863] INFO [GroupCoordinator 1]: Group order-service-group-dlq with generation 2 is now empty (__consumer_offsets-0) (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,864] INFO [GroupCoordinator 1]: Member MemberMetadata(memberId=consumer-order-service-group-4-81f76066-c6ef-4675-9bd7-d4397bdc5bce, groupInstanceId=None, clientId=consumer-order-service-group-4, clientHost=/127.0.0.1, sessionTimeoutMs=30000, rebalanceTimeoutMs=300000, supportedProtocols=List(range)) has left group order-service-group through explicit `LeaveGroup`; client reason: not provided (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,864] INFO [GroupCoordinator 1]: Member MemberMetadata(memberId=consumer-order-service-group-dlq-7-180701d0-857a-4b05-84a8-56fa76795057, groupInstanceId=None, clientId=consumer-order-service-group-dlq-7, clientHost=/127.0.0.1, sessionTimeoutMs=30000, rebalanceTimeoutMs=300000, supportedProtocols=List(range)) has left group order-service-group-dlq through explicit `LeaveGroup`; client reason: not provided (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,886] INFO [BrokerLifecycleManager id=1] The broker is in PENDING_CONTROLLED_SHUTDOWN state, still waiting for the active controller. (kafka.server.BrokerLifecycleManager)
[2025-07-06 10:29:10,906] INFO [ReplicaFetcherManager on broker 1] Removed fetcher for partitions HashSet(__consumer_offsets-22, __consumer_offsets-30, __consumer_offsets-35, __consumer_offsets-37, kafka-test-topic-0, __consumer_offsets-38, __consumer_offsets-13, test-topic-0, order-dlq-0, __consumer_offsets-8, __consumer_offsets-21, order-events-0, __consumer_offsets-4, kafka-test-topic-2, __consumer_offsets-27, __consumer_offsets-7, __consumer_offsets-9, __consumer_offsets-46, kafka-test-topic-1, __consumer_offsets-25, __consumer_offsets-41, __consumer_offsets-33, __consumer_offsets-23, __consumer_offsets-49, __consumer_offsets-47, __consumer_offsets-16, __consumer_offsets-28, __consumer_offsets-31, __consumer_offsets-36, __consumer_offsets-42, __consumer_offsets-3, __consumer_offsets-18, __consumer_offsets-15, __consumer_offsets-24, __consumer_offsets-17, __consumer_offsets-48, __consumer_offsets-19, __consumer_offsets-11, __consumer_offsets-2, __consumer_offsets-43, __consumer_offsets-6, __consumer_offsets-14, __consumer_offsets-20, __consumer_offsets-0, __consumer_offsets-44, __consumer_offsets-39, __consumer_offsets-12, __consumer_offsets-45, order-status-updates-0, __consumer_offsets-1, __consumer_offsets-5, __consumer_offsets-26, __consumer_offsets-29, __consumer_offsets-34, __consumer_offsets-10, __consumer_offsets-32, __consumer_offsets-40) (kafka.server.ReplicaFetcherManager)
[2025-07-06 10:29:10,907] INFO [ReplicaAlterLogDirsManager on broker 1] Removed fetcher for partitions HashSet(__consumer_offsets-22, __consumer_offsets-30, __consumer_offsets-35, __consumer_offsets-37, kafka-test-topic-0, __consumer_offsets-38, __consumer_offsets-13, test-topic-0, order-dlq-0, __consumer_offsets-8, __consumer_offsets-21, order-events-0, __consumer_offsets-4, kafka-test-topic-2, __consumer_offsets-27, __consumer_offsets-7, __consumer_offsets-9, __consumer_offsets-46, kafka-test-topic-1, __consumer_offsets-25, __consumer_offsets-41, __consumer_offsets-33, __consumer_offsets-23, __consumer_offsets-49, __consumer_offsets-47, __consumer_offsets-16, __consumer_offsets-28, __consumer_offsets-31, __consumer_offsets-36, __consumer_offsets-42, __consumer_offsets-3, __consumer_offsets-18, __consumer_offsets-15, __consumer_offsets-24, __consumer_offsets-17, __consumer_offsets-48, __consumer_offsets-19, __consumer_offsets-11, __consumer_offsets-2, __consumer_offsets-43, __consumer_offsets-6, __consumer_offsets-14, __consumer_offsets-20, __consumer_offsets-0, __consumer_offsets-44, __consumer_offsets-39, __consumer_offsets-12, __consumer_offsets-45, order-status-updates-0, __consumer_offsets-1, __consumer_offsets-5, __consumer_offsets-26, __consumer_offsets-29, __consumer_offsets-34, __consumer_offsets-10, __consumer_offsets-32, __consumer_offsets-40) (kafka.server.ReplicaAlterLogDirsManager)
[2025-07-06 10:29:10,944] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 13 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,945] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-13 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,945] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 46 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,945] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-46 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,945] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 9 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,945] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-9 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 42 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-42 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 21 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-21 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 17 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-17 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 30 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-30 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 26 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-26 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 5 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-5 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 38 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-38 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 1 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-1 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 34 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-34 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 16 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-16 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 45 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-45 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 12 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-12 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 41 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-41 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 24 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-24 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 20 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-20 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 49 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-49 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 0 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-0 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 29 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-29 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 25 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-25 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 8 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-8 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 37 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-37 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 4 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-4 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 33 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-33 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 15 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-15 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 48 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-48 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,946] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 11 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,946] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-11 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 44 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-44 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 23 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-23 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 19 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-19 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 32 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-32 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 28 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-28 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 7 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-7 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 40 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-40 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 3 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-3 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 36 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-36 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 47 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-47 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 14 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-14 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 43 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-43 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 10 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-10 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 22 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-22 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 18 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-18 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 31 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-31 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 27 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-27 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 39 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-39 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 6 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-6 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 35 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-35 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,947] INFO [GroupCoordinator 1]: Resigned as the group coordinator for partition 2 in epoch OptionalInt[1] (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,947] INFO [GroupMetadataManager brokerId=1] Scheduling unloading of offsets and group metadata from __consumer_offsets-2 (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,955] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-13 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,956] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-46 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,956] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-9 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,956] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-42 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,956] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-21 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,956] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-17 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,956] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-30 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,956] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-26 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,956] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-5 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,956] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-38 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,956] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-1 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,957] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-34 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,959] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-16 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,959] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-45 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,959] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-12 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,959] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-41 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,959] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-24 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,959] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-20 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,959] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-49 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,960] INFO [GroupCoordinator 1]: Unloading group metadata for order-service-group-dlq with generation 2 (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-0 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 1 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-29 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-25 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-8 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-37 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-4 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-33 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-15 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-48 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-11 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-44 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-23 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-19 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupCoordinator 1]: Unloading group metadata for order-service-group with generation 2 (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-32 for coordinator epoch OptionalInt[1]. Removed 2 cached offsets and 1 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-28 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,961] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-7 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-40 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-3 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-36 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-47 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-14 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-43 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-10 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-22 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-18 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-31 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-27 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-39 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-6 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-35 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,962] INFO [GroupMetadataManager brokerId=1] Finished unloading __consumer_offsets-2 for coordinator epoch OptionalInt[1]. Removed 0 cached offsets and 0 cached groups. (kafka.coordinator.group.GroupMetadataManager)
[2025-07-06 10:29:10,972] INFO [BrokerLifecycleManager id=1] The controller has asked us to exit controlled shutdown. (kafka.server.BrokerLifecycleManager)
[2025-07-06 10:29:10,972] INFO [BrokerLifecycleManager id=1] beginShutdown: shutting down event queue. (org.apache.kafka.queue.KafkaEventQueue)
[2025-07-06 10:29:10,973] INFO [BrokerLifecycleManager id=1] Transitioning from PENDING_CONTROLLED_SHUTDOWN to SHUTTING_DOWN. (kafka.server.BrokerLifecycleManager)
[2025-07-06 10:29:10,973] INFO [broker-1-to-controller-heartbeat-channel-manager]: Shutting down (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:10,973] INFO [broker-1-to-controller-heartbeat-channel-manager]: Stopped (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:10,974] INFO [broker-1-to-controller-heartbeat-channel-manager]: Shutdown completed (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:10,977] INFO [SocketServer listenerType=BROKER, nodeId=1] Stopping socket server request processors (kafka.network.SocketServer)
[2025-07-06 10:29:11,029] INFO Node to controller channel manager for heartbeat shutdown (kafka.server.NodeToControllerChannelManagerImpl)
[2025-07-06 10:29:11,041] INFO [SocketServer listenerType=BROKER, nodeId=1] Stopped socket server request processors (kafka.network.SocketServer)
[2025-07-06 10:29:11,043] INFO [data-plane Kafka Request Handler on Broker 1], shutting down (kafka.server.KafkaRequestHandlerPool)
[2025-07-06 10:29:11,044] INFO [data-plane Kafka Request Handler on Broker 1], shut down completely (kafka.server.KafkaRequestHandlerPool)
[2025-07-06 10:29:11,044] INFO [ExpirationReaper-1-AlterAcls]: Shutting down (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,045] INFO [ExpirationReaper-1-AlterAcls]: Stopped (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,045] INFO [ExpirationReaper-1-AlterAcls]: Shutdown completed (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,046] INFO [KafkaApi-1] Shutdown complete. (kafka.server.KafkaApis)
[2025-07-06 10:29:11,049] INFO [TransactionCoordinator id=1] Shutting down. (kafka.coordinator.transaction.TransactionCoordinator)
[2025-07-06 10:29:11,049] INFO [Transaction State Manager 1]: Shutdown complete (kafka.coordinator.transaction.TransactionStateManager)
[2025-07-06 10:29:11,049] INFO [TxnMarkerSenderThread-1]: Shutting down (kafka.coordinator.transaction.TransactionMarkerChannelManager)
[2025-07-06 10:29:11,050] INFO [TxnMarkerSenderThread-1]: Stopped (kafka.coordinator.transaction.TransactionMarkerChannelManager)
[2025-07-06 10:29:11,050] INFO [TxnMarkerSenderThread-1]: Shutdown completed (kafka.coordinator.transaction.TransactionMarkerChannelManager)
[2025-07-06 10:29:11,051] INFO [TransactionCoordinator id=1] Shutdown complete. (kafka.coordinator.transaction.TransactionCoordinator)
[2025-07-06 10:29:11,052] INFO [GroupCoordinator 1]: Shutting down. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:11,052] INFO [ExpirationReaper-1-Heartbeat]: Shutting down (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,052] INFO [ExpirationReaper-1-Heartbeat]: Stopped (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,053] INFO [ExpirationReaper-1-Heartbeat]: Shutdown completed (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,053] INFO [ExpirationReaper-1-Rebalance]: Shutting down (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,053] INFO [ExpirationReaper-1-Rebalance]: Stopped (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,053] INFO [ExpirationReaper-1-Rebalance]: Shutdown completed (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,053] INFO [GroupCoordinator 1]: Shutdown complete. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 10:29:11,054] INFO [AssignmentsManager id=1]KafkaEventQueue#close: shutting down event queue. (org.apache.kafka.queue.KafkaEventQueue)
[2025-07-06 10:29:11,054] INFO [broker-1-to-controller-directory-assignments-channel-manager]: Shutting down (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:11,054] INFO [broker-1-to-controller-directory-assignments-channel-manager]: Stopped (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:11,054] INFO [broker-1-to-controller-directory-assignments-channel-manager]: Shutdown completed (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:11,055] INFO Node to controller channel manager for directory-assignments shutdown (kafka.server.NodeToControllerChannelManagerImpl)
[2025-07-06 10:29:11,055] INFO [AssignmentsManager id=1]closed event queue. (org.apache.kafka.queue.KafkaEventQueue)
[2025-07-06 10:29:11,055] INFO [ReplicaManager broker=1] Shutting down (kafka.server.ReplicaManager)
[2025-07-06 10:29:11,055] INFO [LogDirFailureHandler]: Shutting down (kafka.server.ReplicaManager$LogDirFailureHandler)
[2025-07-06 10:29:11,056] INFO [LogDirFailureHandler]: Stopped (kafka.server.ReplicaManager$LogDirFailureHandler)
[2025-07-06 10:29:11,056] INFO [LogDirFailureHandler]: Shutdown completed (kafka.server.ReplicaManager$LogDirFailureHandler)
[2025-07-06 10:29:11,056] INFO [ReplicaFetcherManager on broker 1] shutting down (kafka.server.ReplicaFetcherManager)
[2025-07-06 10:29:11,057] INFO [ReplicaFetcherManager on broker 1] shutdown completed (kafka.server.ReplicaFetcherManager)
[2025-07-06 10:29:11,057] INFO [ReplicaAlterLogDirsManager on broker 1] shutting down (kafka.server.ReplicaAlterLogDirsManager)
[2025-07-06 10:29:11,057] INFO [ReplicaAlterLogDirsManager on broker 1] shutdown completed (kafka.server.ReplicaAlterLogDirsManager)
[2025-07-06 10:29:11,057] INFO [ExpirationReaper-1-Fetch]: Shutting down (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,058] INFO [ExpirationReaper-1-Fetch]: Stopped (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,058] INFO [ExpirationReaper-1-Fetch]: Shutdown completed (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,059] INFO [ExpirationReaper-1-RemoteFetch]: Shutting down (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,060] INFO [ExpirationReaper-1-RemoteFetch]: Stopped (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,060] INFO [ExpirationReaper-1-RemoteFetch]: Shutdown completed (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,060] INFO [ExpirationReaper-1-Produce]: Shutting down (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,060] INFO [ExpirationReaper-1-Produce]: Stopped (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,060] INFO [ExpirationReaper-1-Produce]: Shutdown completed (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,061] INFO [ExpirationReaper-1-DeleteRecords]: Shutting down (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,061] INFO [ExpirationReaper-1-DeleteRecords]: Stopped (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,061] INFO [ExpirationReaper-1-DeleteRecords]: Shutdown completed (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,061] INFO [ExpirationReaper-1-ElectLeader]: Shutting down (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,062] INFO [ExpirationReaper-1-ElectLeader]: Stopped (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,062] INFO [ExpirationReaper-1-ElectLeader]: Shutdown completed (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:11,072] INFO [AddPartitionsToTxnSenderThread-1]: Shutting down (kafka.server.AddPartitionsToTxnManager)
[2025-07-06 10:29:11,072] INFO [AddPartitionsToTxnSenderThread-1]: Stopped (kafka.server.AddPartitionsToTxnManager)
[2025-07-06 10:29:11,072] INFO [AddPartitionsToTxnSenderThread-1]: Shutdown completed (kafka.server.AddPartitionsToTxnManager)
[2025-07-06 10:29:11,072] INFO [ReplicaManager broker=1] Shut down completely (kafka.server.ReplicaManager)
[2025-07-06 10:29:11,073] INFO [broker-1-to-controller-alter-partition-channel-manager]: Shutting down (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:11,073] INFO [broker-1-to-controller-alter-partition-channel-manager]: Stopped (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:11,073] INFO [broker-1-to-controller-alter-partition-channel-manager]: Shutdown completed (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:11,073] INFO Node to controller channel manager for alter-partition shutdown (kafka.server.NodeToControllerChannelManagerImpl)
[2025-07-06 10:29:11,073] INFO [broker-1-to-controller-forwarding-channel-manager]: Shutting down (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:11,073] INFO [broker-1-to-controller-forwarding-channel-manager]: Stopped (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:11,073] INFO [broker-1-to-controller-forwarding-channel-manager]: Shutdown completed (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:11,074] INFO Node to controller channel manager for forwarding shutdown (kafka.server.NodeToControllerChannelManagerImpl)
[2025-07-06 10:29:11,074] INFO Shutting down. (kafka.log.LogManager)
[2025-07-06 10:29:11,075] INFO [kafka-log-cleaner-thread-0]: Shutting down (kafka.log.LogCleaner$CleanerThread)
[2025-07-06 10:29:11,076] INFO [kafka-log-cleaner-thread-0]: Stopped (kafka.log.LogCleaner$CleanerThread)
[2025-07-06 10:29:11,076] INFO [kafka-log-cleaner-thread-0]: Shutdown completed (kafka.log.LogCleaner$CleanerThread)
[2025-07-06 10:29:11,109] INFO [ProducerStateManager partition=__consumer_offsets-0] Wrote producer snapshot at offset 2 with 0 producer ids in 4 ms. (org.apache.kafka.storage.internals.log.ProducerStateManager)
[2025-07-06 10:29:11,151] INFO [ProducerStateManager partition=kafka-test-topic-1] Wrote producer snapshot at offset 1 with 1 producer ids in 14 ms. (org.apache.kafka.storage.internals.log.ProducerStateManager)
[2025-07-06 10:29:11,191] INFO [ProducerStateManager partition=order-events-0] Wrote producer snapshot at offset 7 with 1 producer ids in 4 ms. (org.apache.kafka.storage.internals.log.ProducerStateManager)
[2025-07-06 10:29:11,233] INFO [ProducerStateManager partition=__consumer_offsets-8] Wrote producer snapshot at offset 2 with 0 producer ids in 4 ms. (org.apache.kafka.storage.internals.log.ProducerStateManager)
[2025-07-06 10:29:11,286] INFO [ProducerStateManager partition=__consumer_offsets-32] Wrote producer snapshot at offset 11 with 0 producer ids in 3 ms. (org.apache.kafka.storage.internals.log.ProducerStateManager)
[2025-07-06 10:29:11,421] INFO [ProducerStateManager partition=test-topic-0] Wrote producer snapshot at offset 1 with 1 producer ids in 3 ms. (org.apache.kafka.storage.internals.log.ProducerStateManager)
[2025-07-06 10:29:11,460] INFO [ProducerStateManager partition=order-status-updates-0] Wrote producer snapshot at offset 2 with 1 producer ids in 3 ms. (org.apache.kafka.storage.internals.log.ProducerStateManager)
[2025-07-06 10:29:11,471] INFO [ProducerStateManager partition=__consumer_offsets-28] Wrote producer snapshot at offset 2 with 0 producer ids in 4 ms. (org.apache.kafka.storage.internals.log.ProducerStateManager)
[2025-07-06 10:29:11,625] INFO Shutdown complete. (kafka.log.LogManager)
[2025-07-06 10:29:11,626] INFO [broker-1-ThrottledChannelReaper-Fetch]: Shutting down (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:11,628] INFO [broker-1-ThrottledChannelReaper-Fetch]: Stopped (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:11,628] INFO [broker-1-ThrottledChannelReaper-Fetch]: Shutdown completed (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:11,628] INFO [broker-1-ThrottledChannelReaper-Produce]: Shutting down (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:11,628] INFO [broker-1-ThrottledChannelReaper-Produce]: Stopped (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:11,628] INFO [broker-1-ThrottledChannelReaper-Produce]: Shutdown completed (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:11,628] INFO [broker-1-ThrottledChannelReaper-Request]: Shutting down (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:11,629] INFO [broker-1-ThrottledChannelReaper-Request]: Stopped (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:11,629] INFO [broker-1-ThrottledChannelReaper-Request]: Shutdown completed (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:11,629] INFO [broker-1-ThrottledChannelReaper-ControllerMutation]: Shutting down (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:11,629] INFO [broker-1-ThrottledChannelReaper-ControllerMutation]: Stopped (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:11,629] INFO [broker-1-ThrottledChannelReaper-ControllerMutation]: Shutdown completed (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:11,629] INFO [SocketServer listenerType=BROKER, nodeId=1] Shutting down socket server (kafka.network.SocketServer)
[2025-07-06 10:29:11,646] INFO [SocketServer listenerType=BROKER, nodeId=1] Shutdown completed (kafka.network.SocketServer)
[2025-07-06 10:29:11,647] INFO Broker and topic stats closed (kafka.server.BrokerTopicStats)
[2025-07-06 10:29:11,647] INFO [BrokerLifecycleManager id=1] closed event queue. (org.apache.kafka.queue.KafkaEventQueue)
[2025-07-06 10:29:11,648] INFO [client-metrics-reaper]: Shutting down (org.apache.kafka.server.util.timer.SystemTimerReaper$Reaper)
[2025-07-06 10:29:11,649] INFO [client-metrics-reaper]: Stopped (org.apache.kafka.server.util.timer.SystemTimerReaper$Reaper)
[2025-07-06 10:29:11,649] INFO [client-metrics-reaper]: Shutdown completed (org.apache.kafka.server.util.timer.SystemTimerReaper$Reaper)
[2025-07-06 10:29:11,650] INFO [BrokerServer id=1] shut down completed (kafka.server.BrokerServer)
[2025-07-06 10:29:11,650] INFO [BrokerServer id=1] Transition from SHUTTING_DOWN to SHUTDOWN (kafka.server.BrokerServer)
[2025-07-06 10:29:11,651] INFO [ControllerServer id=1] shutting down (kafka.server.ControllerServer)
[2025-07-06 10:29:11,651] INFO [raft-expiration-reaper]: Shutting down (kafka.raft.TimingWheelExpirationService$ExpiredOperationReaper)
[2025-07-06 10:29:11,840] INFO [raft-expiration-reaper]: Stopped (kafka.raft.TimingWheelExpirationService$ExpiredOperationReaper)
[2025-07-06 10:29:11,840] INFO [raft-expiration-reaper]: Shutdown completed (kafka.raft.TimingWheelExpirationService$ExpiredOperationReaper)
[2025-07-06 10:29:11,842] INFO [kafka-1-raft-io-thread]: Shutting down (org.apache.kafka.raft.KafkaRaftClientDriver)
[2025-07-06 10:29:11,842] INFO [RaftManager id=1] Beginning graceful shutdown (org.apache.kafka.raft.KafkaRaftClient)
[2025-07-06 10:29:11,844] INFO [RaftManager id=1] Graceful shutdown completed (org.apache.kafka.raft.KafkaRaftClient)
[2025-07-06 10:29:11,844] INFO [RaftManager id=1] Completed graceful shutdown of RaftClient (org.apache.kafka.raft.KafkaRaftClientDriver)
[2025-07-06 10:29:11,844] INFO [kafka-1-raft-io-thread]: Stopped (org.apache.kafka.raft.KafkaRaftClientDriver)
[2025-07-06 10:29:11,844] INFO [kafka-1-raft-io-thread]: Shutdown completed (org.apache.kafka.raft.KafkaRaftClientDriver)
[2025-07-06 10:29:11,848] INFO [kafka-1-raft-outbound-request-thread]: Shutting down (org.apache.kafka.raft.KafkaNetworkChannel$SendThread)
[2025-07-06 10:29:11,849] INFO [kafka-1-raft-outbound-request-thread]: Stopped (org.apache.kafka.raft.KafkaNetworkChannel$SendThread)
[2025-07-06 10:29:11,849] INFO [kafka-1-raft-outbound-request-thread]: Shutdown completed (org.apache.kafka.raft.KafkaNetworkChannel$SendThread)
[2025-07-06 10:29:11,854] INFO [ProducerStateManager partition=__cluster_metadata-0] Wrote producer snapshot at offset 8552 with 0 producer ids in 4 ms. (org.apache.kafka.storage.internals.log.ProducerStateManager)
[2025-07-06 10:29:11,858] INFO [ControllerRegistrationManager id=1 incarnation=dDkvVYmZRpiis_Rf1Fcrrg] beginShutdown: shutting down event queue. (org.apache.kafka.queue.KafkaEventQueue)
[2025-07-06 10:29:11,859] INFO [ControllerRegistrationManager id=1 incarnation=dDkvVYmZRpiis_Rf1Fcrrg] shutting down. (kafka.server.ControllerRegistrationManager)
[2025-07-06 10:29:11,859] INFO [controller-1-to-controller-registration-channel-manager]: Shutting down (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:11,859] INFO [controller-1-to-controller-registration-channel-manager]: Stopped (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:11,859] INFO [controller-1-to-controller-registration-channel-manager]: Shutdown completed (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:11,859] INFO Node to controller channel manager for registration shutdown (kafka.server.NodeToControllerChannelManagerImpl)
[2025-07-06 10:29:11,859] INFO [ControllerRegistrationManager id=1 incarnation=dDkvVYmZRpiis_Rf1Fcrrg] closed event queue. (org.apache.kafka.queue.KafkaEventQueue)
[2025-07-06 10:29:11,860] INFO [controller-1-to-controller-registration-channel-manager]: Shutdown completed (kafka.server.NodeToControllerRequestThread)
[2025-07-06 10:29:11,860] WARN [NodeToControllerChannelManager id=1 name=registration] Attempting to close NetworkClient that has already been closed. (org.apache.kafka.clients.NetworkClient)
[2025-07-06 10:29:11,860] INFO Node to controller channel manager for registration shutdown (kafka.server.NodeToControllerChannelManagerImpl)
[2025-07-06 10:29:11,860] INFO [ControllerRegistrationManager id=1 incarnation=dDkvVYmZRpiis_Rf1Fcrrg] closed event queue. (org.apache.kafka.queue.KafkaEventQueue)
[2025-07-06 10:29:11,862] INFO [SocketServer listenerType=CONTROLLER, nodeId=1] Stopping socket server request processors (kafka.network.SocketServer)
[2025-07-06 10:29:11,865] INFO [SocketServer listenerType=CONTROLLER, nodeId=1] Stopped socket server request processors (kafka.network.SocketServer)
[2025-07-06 10:29:11,867] INFO [QuorumController id=1] QuorumController#beginShutdown: shutting down event queue. (org.apache.kafka.queue.KafkaEventQueue)
[2025-07-06 10:29:11,867] INFO [SocketServer listenerType=CONTROLLER, nodeId=1] Shutting down socket server (kafka.network.SocketServer)
[2025-07-06 10:29:11,877] INFO [SocketServer listenerType=CONTROLLER, nodeId=1] Shutdown completed (kafka.network.SocketServer)
[2025-07-06 10:29:12,940] INFO [data-plane Kafka Request Handler on Broker 1], shutting down (kafka.server.KafkaRequestHandlerPool)
[2025-07-06 10:29:12,941] INFO [data-plane Kafka Request Handler on Broker 1], shut down completely (kafka.server.KafkaRequestHandlerPool)
[2025-07-06 10:29:12,941] INFO [ExpirationReaper-1-AlterAcls]: Shutting down (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:12,942] INFO [ExpirationReaper-1-AlterAcls]: Stopped (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:12,942] INFO [ExpirationReaper-1-AlterAcls]: Shutdown completed (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 10:29:12,943] INFO [controller-1-ThrottledChannelReaper-Fetch]: Shutting down (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:12,943] INFO [controller-1-ThrottledChannelReaper-Fetch]: Stopped (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:12,943] INFO [controller-1-ThrottledChannelReaper-Fetch]: Shutdown completed (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:12,944] INFO [controller-1-ThrottledChannelReaper-Produce]: Shutting down (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:12,944] INFO [controller-1-ThrottledChannelReaper-Produce]: Stopped (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:12,944] INFO [controller-1-ThrottledChannelReaper-Produce]: Shutdown completed (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:12,944] INFO [controller-1-ThrottledChannelReaper-Request]: Shutting down (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:12,944] INFO [controller-1-ThrottledChannelReaper-Request]: Stopped (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:12,944] INFO [controller-1-ThrottledChannelReaper-Request]: Shutdown completed (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:12,944] INFO [controller-1-ThrottledChannelReaper-ControllerMutation]: Shutting down (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:12,944] INFO [controller-1-ThrottledChannelReaper-ControllerMutation]: Stopped (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:12,944] INFO [controller-1-ThrottledChannelReaper-ControllerMutation]: Shutdown completed (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 10:29:12,945] INFO [QuorumController id=1] closed event queue. (org.apache.kafka.queue.KafkaEventQueue)
[2025-07-06 10:29:12,946] INFO [SharedServer id=1] Stopping SharedServer (kafka.server.SharedServer)
[2025-07-06 10:29:12,946] INFO [MetadataLoader id=1] beginShutdown: shutting down event queue. (org.apache.kafka.queue.KafkaEventQueue)
[2025-07-06 10:29:12,947] INFO [SnapshotGenerator id=1] close: shutting down event queue. (org.apache.kafka.queue.KafkaEventQueue)
[2025-07-06 10:29:12,947] INFO [SnapshotGenerator id=1] closed event queue. (org.apache.kafka.queue.KafkaEventQueue)
[2025-07-06 10:29:12,947] INFO [MetadataLoader id=1] closed event queue. (org.apache.kafka.queue.KafkaEventQueue)
[2025-07-06 10:29:12,948] INFO [SnapshotGenerator id=1] closed event queue. (org.apache.kafka.queue.KafkaEventQueue)
[2025-07-06 10:29:12,949] INFO Metrics scheduler closed (org.apache.kafka.common.metrics.Metrics)
[2025-07-06 10:29:12,949] INFO Closing reporter org.apache.kafka.common.metrics.JmxReporter (org.apache.kafka.common.metrics.Metrics)
[2025-07-06 10:29:12,949] INFO Metrics reporters closed (org.apache.kafka.common.metrics.Metrics)
[2025-07-06 10:29:12,950] INFO App info kafka.server for 1 unregistered (org.apache.kafka.common.utils.AppInfoParser)
[2025-07-06 10:29:12,950] INFO App info kafka.server for 1 unregistered (org.apache.kafka.common.utils.AppInfoParser)
