[2025-07-06 09:01:14,246] INFO Registered kafka:type=kafka.Log4jController MBean (kafka.utils.Log4jControllerRegistration$)
[2025-07-06 09:01:14,468] INFO Setting -D jdk.tls.rejectClientInitiatedRenegotiation=true to disable client-initiated TLS renegotiation (org.apache.zookeeper.common.X509Util)
[2025-07-06 09:01:14,470] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:01:14,716] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:01:14,724] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:01:14,754] INFO Registered signal handlers for TERM, INT, HUP (org.apache.kafka.common.utils.LoggingSignalHandler)
[2025-07-06 09:01:14,758] INFO [ControllerServer id=1] Starting controller (kafka.server.ControllerServer)
[2025-07-06 09:01:14,762] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:01:15,194] INFO Updated connection-accept-rate max connection creation rate to 2147483647 (kafka.network.ConnectionQuotas)
[2025-07-06 09:01:15,272] INFO [SocketServer listenerType=CONTROLLER, nodeId=1] Created data-plane acceptor and processors for endpoint : ListenerName(CONTROLLER) (kafka.network.SocketServer)
[2025-07-06 09:01:15,277] INFO CONTROLLER: resolved wildcard host to LAPTOP-H9435UC9. (org.apache.kafka.metadata.ListenerInfo)
[2025-07-06 09:01:15,283] INFO authorizerStart completed for endpoint CONTROLLER. Endpoint is now READY. (org.apache.kafka.server.network.EndpointReadyFutures)
[2025-07-06 09:01:15,284] INFO [SharedServer id=1] Starting SharedServer (kafka.server.SharedServer)
[2025-07-06 09:01:15,286] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:01:15,347] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Loading producer state till offset 0 with message format version 2 (kafka.log.UnifiedLog$)
[2025-07-06 09:01:15,347] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Reloading from producer snapshot and rebuilding producer state from offset 0 (kafka.log.UnifiedLog$)
[2025-07-06 09:01:15,347] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Producer state recovery took 0ms for snapshot load and 0ms for segment recovery from offset 0 (kafka.log.UnifiedLog$)
[2025-07-06 09:01:15,382] INFO Initialized snapshots with IDs SortedSet() from /tmp/kraft-combined-logs/__cluster_metadata-0 (kafka.raft.KafkaMetadataLog$)
[2025-07-06 09:01:15,397] INFO [raft-expiration-reaper]: Starting (kafka.raft.TimingWheelExpirationService$ExpiredOperationReaper)
[2025-07-06 09:01:15,414] INFO [RaftManager id=1] Reading KRaft snapshot and log as part of the initialization (org.apache.kafka.raft.KafkaRaftClient)
[2025-07-06 09:01:15,416] INFO [RaftManager id=1] Starting request manager with static voters: [localhost:9093 (id: 1 rack: null)] (org.apache.kafka.raft.KafkaRaftClient)
[2025-07-06 09:01:15,589] INFO [RaftManager id=1] Completed transition to Unattached(epoch=0, voters=[1], electionTimeoutMs=1107) from null (org.apache.kafka.raft.QuorumState)
[2025-07-06 09:01:15,599] INFO [RaftManager id=1] Completed transition to CandidateState(localId=1, localDirectoryId=fByaQt4RR3XuGwEgMy431g,epoch=1, retries=1, voteStates={1=GRANTED}, highWatermark=Optional.empty, electionTimeoutMs=1729) from Unattached(epoch=0, voters=[1], electionTimeoutMs=1107) (org.apache.kafka.raft.QuorumState)
[2025-07-06 09:01:15,614] INFO [RaftManager id=1] Completed transition to Leader(localId=1, epoch=1, epochStartOffset=0, highWatermark=Optional.empty, voterStates={1=ReplicaState(nodeId=1, endOffset=Optional.empty, lastFetchTimestamp=-1, lastCaughtUpTimestamp=-1, hasAcknowledgedLeader=true)}) from CandidateState(localId=1, localDirectoryId=fByaQt4RR3XuGwEgMy431g,epoch=1, retries=1, voteStates={1=GRANTED}, highWatermark=Optional.empty, electionTimeoutMs=1729) (org.apache.kafka.raft.QuorumState)
[2025-07-06 09:01:15,638] INFO [kafka-1-raft-outbound-request-thread]: Starting (org.apache.kafka.raft.KafkaNetworkChannel$SendThread)
[2025-07-06 09:01:15,638] INFO [kafka-1-raft-io-thread]: Starting (org.apache.kafka.raft.KafkaRaftClientDriver)
[2025-07-06 09:01:15,721] INFO [MetadataLoader id=1] initializeNewPublishers: the loader is still catching up because we still don't know the high water mark yet. (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:15,722] INFO [ControllerServer id=1] Waiting for controller quorum voters future (kafka.server.ControllerServer)
[2025-07-06 09:01:15,722] INFO [ControllerServer id=1] Finished waiting for controller quorum voters future (kafka.server.ControllerServer)
[2025-07-06 09:01:15,725] INFO [RaftManager id=1] High watermark set to LogOffsetMetadata(offset=1, metadata=Optional[(segmentBaseOffset=0,relativePositionInSegment=91)]) for the first time for epoch 1 based on indexOfHw 0 and voters [ReplicaState(nodeId=1, endOffset=Optional[LogOffsetMetadata(offset=1, metadata=Optional[(segmentBaseOffset=0,relativePositionInSegment=91)])], lastFetchTimestamp=-1, lastCaughtUpTimestamp=-1, hasAcknowledgedLeader=true)] (org.apache.kafka.raft.LeaderState)
[2025-07-06 09:01:15,740] INFO [RaftManager id=1] Registered the listener org.apache.kafka.image.loader.MetadataLoader@2008525705 (org.apache.kafka.raft.KafkaRaftClient)
[2025-07-06 09:01:15,743] INFO [MetadataLoader id=1] maybePublishMetadata(LOG_DELTA): The loader is still catching up because we have not loaded a controller record as of offset 0 and high water mark is 1 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:15,750] INFO [RaftManager id=1] Registered the listener org.apache.kafka.controller.QuorumController$QuorumMetaLogListener@799729949 (org.apache.kafka.raft.KafkaRaftClient)
[2025-07-06 09:01:15,757] INFO [controller-1-ThrottledChannelReaper-Fetch]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:01:15,758] INFO [controller-1-ThrottledChannelReaper-Produce]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:01:15,759] INFO [controller-1-ThrottledChannelReaper-Request]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:01:15,760] INFO [controller-1-ThrottledChannelReaper-ControllerMutation]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:01:15,778] INFO [ExpirationReaper-1-AlterAcls]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 09:01:15,792] INFO [ControllerServer id=1] Waiting for the controller metadata publishers to be installed (kafka.server.ControllerServer)
[2025-07-06 09:01:15,795] INFO [MetadataLoader id=1] maybePublishMetadata(LOG_DELTA): The loader finished catching up to the current high water mark of 5 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:15,797] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing SnapshotGenerator with a snapshot at offset 4 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:15,798] INFO [ControllerServer id=1] Finished waiting for the controller metadata publishers to be installed (kafka.server.ControllerServer)
[2025-07-06 09:01:15,798] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing KRaftMetadataCachePublisher with a snapshot at offset 4 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:15,798] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing FeaturesPublisher with a snapshot at offset 4 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:15,798] INFO [ControllerServer id=1] Loaded new metadata Features(metadataVersion=3.8-IV0, finalizedFeatures={metadata.version=20}, finalizedFeaturesEpoch=4). (org.apache.kafka.metadata.publisher.FeaturesPublisher)
[2025-07-06 09:01:15,798] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing ControllerRegistrationsPublisher with a snapshot at offset 4 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:15,798] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing ControllerRegistrationManager with a snapshot at offset 4 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:15,799] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing DynamicConfigPublisher controller id=1 with a snapshot at offset 4 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:15,800] INFO [SocketServer listenerType=CONTROLLER, nodeId=1] Enabling request processing. (kafka.network.SocketServer)
[2025-07-06 09:01:15,801] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing DynamicClientQuotaPublisher controller id=1 with a snapshot at offset 4 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:15,806] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing ScramPublisher controller id=1 with a snapshot at offset 4 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:15,847] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing DelegationTokenPublisher controller id=1 with a snapshot at offset 4 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:15,878] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing ControllerMetadataMetricsPublisher with a snapshot at offset 4 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:15,880] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing AclPublisher controller id=1 with a snapshot at offset 4 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:15,880] INFO Awaiting socket connections on 0.0.0.0:9093. (kafka.network.DataPlaneAcceptor)
[2025-07-06 09:01:15,894] INFO [controller-1-to-controller-registration-channel-manager]: Starting (kafka.server.NodeToControllerRequestThread)
[2025-07-06 09:01:15,895] INFO [ControllerServer id=1] Waiting for all of the authorizer futures to be completed (kafka.server.ControllerServer)
[2025-07-06 09:01:15,895] INFO [ControllerServer id=1] Finished waiting for all of the authorizer futures to be completed (kafka.server.ControllerServer)
[2025-07-06 09:01:15,895] INFO [ControllerServer id=1] Waiting for all of the SocketServer Acceptors to be started (kafka.server.ControllerServer)
[2025-07-06 09:01:15,895] INFO [ControllerRegistrationManager id=1 incarnation=dDkvVYmZRpiis_Rf1Fcrrg] initialized channel manager. (kafka.server.ControllerRegistrationManager)
[2025-07-06 09:01:15,895] INFO [ControllerServer id=1] Finished waiting for all of the SocketServer Acceptors to be started (kafka.server.ControllerServer)
[2025-07-06 09:01:15,896] INFO [controller-1-to-controller-registration-channel-manager]: Recorded new KRaft controller, from now on will use node localhost:9093 (id: 1 rack: null) (kafka.server.NodeToControllerRequestThread)
[2025-07-06 09:01:15,897] INFO [BrokerServer id=1] Transition from SHUTDOWN to STARTING (kafka.server.BrokerServer)
[2025-07-06 09:01:15,897] INFO [ControllerRegistrationManager id=1 incarnation=dDkvVYmZRpiis_Rf1Fcrrg] sendControllerRegistration: attempting to send ControllerRegistrationRequestData(controllerId=1, incarnationId=dDkvVYmZRpiis_Rf1Fcrrg, zkMigrationReady=false, listeners=[Listener(name='CONTROLLER', host='LAPTOP-H9435UC9.', port=9093, securityProtocol=0)], features=[Feature(name='metadata.version', minSupportedVersion=1, maxSupportedVersion=20)]) (kafka.server.ControllerRegistrationManager)
[2025-07-06 09:01:15,897] INFO [BrokerServer id=1] Starting broker (kafka.server.BrokerServer)
[2025-07-06 09:01:15,901] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:01:15,903] INFO [broker-1-ThrottledChannelReaper-Fetch]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:01:15,903] INFO [broker-1-ThrottledChannelReaper-Produce]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:01:15,903] INFO [broker-1-ThrottledChannelReaper-Request]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:01:15,904] INFO [broker-1-ThrottledChannelReaper-ControllerMutation]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:01:15,925] INFO [BrokerServer id=1] Waiting for controller quorum voters future (kafka.server.BrokerServer)
[2025-07-06 09:01:15,925] INFO [BrokerServer id=1] Finished waiting for controller quorum voters future (kafka.server.BrokerServer)
[2025-07-06 09:01:15,927] INFO [broker-1-to-controller-forwarding-channel-manager]: Starting (kafka.server.NodeToControllerRequestThread)
[2025-07-06 09:01:15,927] INFO [broker-1-to-controller-forwarding-channel-manager]: Recorded new KRaft controller, from now on will use node localhost:9093 (id: 1 rack: null) (kafka.server.NodeToControllerRequestThread)
[2025-07-06 09:01:15,932] INFO [client-metrics-reaper]: Starting (org.apache.kafka.server.util.timer.SystemTimerReaper$Reaper)
[2025-07-06 09:01:15,958] INFO Updated connection-accept-rate max connection creation rate to 2147483647 (kafka.network.ConnectionQuotas)
[2025-07-06 09:01:15,971] INFO [SocketServer listenerType=BROKER, nodeId=1] Created data-plane acceptor and processors for endpoint : ListenerName(PLAINTEXT) (kafka.network.SocketServer)
[2025-07-06 09:01:15,977] INFO [broker-1-to-controller-alter-partition-channel-manager]: Starting (kafka.server.NodeToControllerRequestThread)
[2025-07-06 09:01:15,977] INFO [broker-1-to-controller-alter-partition-channel-manager]: Recorded new KRaft controller, from now on will use node localhost:9093 (id: 1 rack: null) (kafka.server.NodeToControllerRequestThread)
[2025-07-06 09:01:15,987] INFO [broker-1-to-controller-directory-assignments-channel-manager]: Starting (kafka.server.NodeToControllerRequestThread)
[2025-07-06 09:01:15,987] INFO [broker-1-to-controller-directory-assignments-channel-manager]: Recorded new KRaft controller, from now on will use node localhost:9093 (id: 1 rack: null) (kafka.server.NodeToControllerRequestThread)
[2025-07-06 09:01:16,000] INFO [ExpirationReaper-1-Produce]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 09:01:16,001] INFO [ExpirationReaper-1-Fetch]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 09:01:16,002] INFO [ExpirationReaper-1-DeleteRecords]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 09:01:16,005] INFO [ExpirationReaper-1-ElectLeader]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 09:01:16,005] INFO [ExpirationReaper-1-RemoteFetch]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 09:01:16,012] INFO [ControllerRegistrationManager id=1 incarnation=dDkvVYmZRpiis_Rf1Fcrrg] Our registration has been persisted to the metadata log. (kafka.server.ControllerRegistrationManager)
[2025-07-06 09:01:16,013] INFO [ControllerRegistrationManager id=1 incarnation=dDkvVYmZRpiis_Rf1Fcrrg] RegistrationResponseHandler: controller acknowledged ControllerRegistrationRequest. (kafka.server.ControllerRegistrationManager)
[2025-07-06 09:01:16,022] INFO [ExpirationReaper-1-Heartbeat]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 09:01:16,022] INFO [ExpirationReaper-1-Rebalance]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 09:01:16,058] INFO Unable to read the broker epoch in /tmp/kraft-combined-logs. (kafka.log.LogManager)
[2025-07-06 09:01:16,059] INFO [broker-1-to-controller-heartbeat-channel-manager]: Starting (kafka.server.NodeToControllerRequestThread)
[2025-07-06 09:01:16,059] INFO [broker-1-to-controller-heartbeat-channel-manager]: Recorded new KRaft controller, from now on will use node localhost:9093 (id: 1 rack: null) (kafka.server.NodeToControllerRequestThread)
[2025-07-06 09:01:16,061] INFO [BrokerLifecycleManager id=1] Incarnation qbFTAsucR1iGd82hmTLRTw of broker 1 in cluster zio5HxucRweCqKtfvhb1qg is now STARTING. (kafka.server.BrokerLifecycleManager)
[2025-07-06 09:01:16,081] INFO [ExpirationReaper-1-AlterAcls]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 09:01:16,100] INFO [BrokerLifecycleManager id=1] Successfully registered broker 1 with broker epoch 6 (kafka.server.BrokerLifecycleManager)
[2025-07-06 09:01:16,103] INFO [BrokerServer id=1] Waiting for the broker metadata publishers to be installed (kafka.server.BrokerServer)
[2025-07-06 09:01:16,103] INFO [BrokerServer id=1] Finished waiting for the broker metadata publishers to be installed (kafka.server.BrokerServer)
[2025-07-06 09:01:16,103] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing MetadataVersionPublisher(id=1) with a snapshot at offset 6 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:16,103] INFO [BrokerServer id=1] Waiting for the controller to acknowledge that we are caught up (kafka.server.BrokerServer)
[2025-07-06 09:01:16,104] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing BrokerMetadataPublisher with a snapshot at offset 6 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:16,104] INFO [BrokerMetadataPublisher id=1] Publishing initial metadata at offset OffsetAndEpoch(offset=6, epoch=1) with metadata.version 3.8-IV0. (kafka.server.metadata.BrokerMetadataPublisher)
[2025-07-06 09:01:16,105] INFO Loading logs from log dirs ArrayBuffer(/tmp/kraft-combined-logs) (kafka.log.LogManager)
[2025-07-06 09:01:16,106] INFO [BrokerLifecycleManager id=1] The broker has caught up. Transitioning from STARTING to RECOVERY. (kafka.server.BrokerLifecycleManager)
[2025-07-06 09:01:16,106] INFO [BrokerServer id=1] Finished waiting for the controller to acknowledge that we are caught up (kafka.server.BrokerServer)
[2025-07-06 09:01:16,106] INFO [BrokerServer id=1] Waiting for the initial broker metadata update to be published (kafka.server.BrokerServer)
[2025-07-06 09:01:16,110] INFO No logs found to be loaded in /tmp/kraft-combined-logs (kafka.log.LogManager)
[2025-07-06 09:01:16,116] INFO Loaded 0 logs in 10ms (kafka.log.LogManager)
[2025-07-06 09:01:16,117] INFO Starting log cleanup with a period of 300000 ms. (kafka.log.LogManager)
[2025-07-06 09:01:16,118] INFO Starting log flusher with a default period of 9223372036854775807 ms. (kafka.log.LogManager)
[2025-07-06 09:01:16,118] INFO [BrokerLifecycleManager id=1] The broker is in RECOVERY. (kafka.server.BrokerLifecycleManager)
[2025-07-06 09:01:16,227] INFO [kafka-log-cleaner-thread-0]: Starting (kafka.log.LogCleaner$CleanerThread)
[2025-07-06 09:01:16,231] INFO [LogDirFailureHandler]: Starting (kafka.server.ReplicaManager$LogDirFailureHandler)
[2025-07-06 09:01:16,232] INFO [AddPartitionsToTxnSenderThread-1]: Starting (kafka.server.AddPartitionsToTxnManager)
[2025-07-06 09:01:16,233] INFO [GroupCoordinator 1]: Starting up. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 09:01:16,238] INFO [GroupCoordinator 1]: Startup complete. (kafka.coordinator.group.GroupCoordinator)
[2025-07-06 09:01:16,239] INFO [TransactionCoordinator id=1] Starting up. (kafka.coordinator.transaction.TransactionCoordinator)
[2025-07-06 09:01:16,241] INFO [TxnMarkerSenderThread-1]: Starting (kafka.coordinator.transaction.TransactionMarkerChannelManager)
[2025-07-06 09:01:16,242] INFO [TransactionCoordinator id=1] Startup complete. (kafka.coordinator.transaction.TransactionCoordinator)
[2025-07-06 09:01:16,249] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing BrokerRegistrationTracker(id=1) with a snapshot at offset 6 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:01:16,249] INFO [BrokerServer id=1] Finished waiting for the initial broker metadata update to be published (kafka.server.BrokerServer)
[2025-07-06 09:01:16,251] INFO KafkaConfig values: 
	advertised.listeners = PLAINTEXT://localhost:9092
	alter.config.policy.class.name = null
	alter.log.dirs.replication.quota.window.num = 11
	alter.log.dirs.replication.quota.window.size.seconds = 1
	authorizer.class.name = 
	auto.create.topics.enable = true
	auto.include.jmx.reporter = true
	auto.leader.rebalance.enable = true
	background.threads = 10
	broker.heartbeat.interval.ms = 2000
	broker.id = 1
	broker.id.generation.enable = true
	broker.rack = null
	broker.session.timeout.ms = 9000
	client.quota.callback.class = null
	compression.gzip.level = -1
	compression.lz4.level = 9
	compression.type = producer
	compression.zstd.level = 3
	connection.failed.authentication.delay.ms = 100
	connections.max.idle.ms = 600000
	connections.max.reauth.ms = 0
	control.plane.listener.name = null
	controlled.shutdown.enable = true
	controlled.shutdown.max.retries = 3
	controlled.shutdown.retry.backoff.ms = 5000
	controller.listener.names = CONTROLLER
	controller.quorum.append.linger.ms = 25
	controller.quorum.bootstrap.servers = []
	controller.quorum.election.backoff.max.ms = 1000
	controller.quorum.election.timeout.ms = 1000
	controller.quorum.fetch.timeout.ms = 2000
	controller.quorum.request.timeout.ms = 2000
	controller.quorum.retry.backoff.ms = 20
	controller.quorum.voters = [1@localhost:9093]
	controller.quota.window.num = 11
	controller.quota.window.size.seconds = 1
	controller.socket.timeout.ms = 30000
	create.topic.policy.class.name = null
	default.replication.factor = 1
	delegation.token.expiry.check.interval.ms = 3600000
	delegation.token.expiry.time.ms = 86400000
	delegation.token.master.key = null
	delegation.token.max.lifetime.ms = 604800000
	delegation.token.secret.key = null
	delete.records.purgatory.purge.interval.requests = 1
	delete.topic.enable = true
	early.start.listeners = null
	eligible.leader.replicas.enable = false
	fetch.max.bytes = 57671680
	fetch.purgatory.purge.interval.requests = 1000
	group.consumer.assignors = [org.apache.kafka.coordinator.group.assignor.UniformAssignor, org.apache.kafka.coordinator.group.assignor.RangeAssignor]
	group.consumer.heartbeat.interval.ms = 5000
	group.consumer.max.heartbeat.interval.ms = 15000
	group.consumer.max.session.timeout.ms = 60000
	group.consumer.max.size = 2147483647
	group.consumer.migration.policy = disabled
	group.consumer.min.heartbeat.interval.ms = 5000
	group.consumer.min.session.timeout.ms = 45000
	group.consumer.session.timeout.ms = 45000
	group.coordinator.append.linger.ms = 10
	group.coordinator.new.enable = false
	group.coordinator.rebalance.protocols = [classic]
	group.coordinator.threads = 1
	group.initial.rebalance.delay.ms = 3000
	group.max.session.timeout.ms = 1800000
	group.max.size = 2147483647
	group.min.session.timeout.ms = 6000
	initial.broker.registration.timeout.ms = 60000
	inter.broker.listener.name = PLAINTEXT
	inter.broker.protocol.version = 3.8-IV0
	kafka.metrics.polling.interval.secs = 10
	kafka.metrics.reporters = []
	leader.imbalance.check.interval.seconds = 300
	leader.imbalance.per.broker.percentage = 10
	listener.security.protocol.map = CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,SSL:SSL,SASL_PLAINTEXT:SASL_PLAINTEXT,SASL_SSL:SASL_SSL
	listeners = PLAINTEXT://:9092,CONTROLLER://:9093
	log.cleaner.backoff.ms = 15000
	log.cleaner.dedupe.buffer.size = 134217728
	log.cleaner.delete.retention.ms = 86400000
	log.cleaner.enable = true
	log.cleaner.io.buffer.load.factor = 0.9
	log.cleaner.io.buffer.size = 524288
	log.cleaner.io.max.bytes.per.second = 1.7976931348623157E308
	log.cleaner.max.compaction.lag.ms = 9223372036854775807
	log.cleaner.min.cleanable.ratio = 0.5
	log.cleaner.min.compaction.lag.ms = 0
	log.cleaner.threads = 1
	log.cleanup.policy = [delete]
	log.dir = /tmp/kafka-logs
	log.dir.failure.timeout.ms = 30000
	log.dirs = /tmp/kraft-combined-logs
	log.flush.interval.messages = 9223372036854775807
	log.flush.interval.ms = null
	log.flush.offset.checkpoint.interval.ms = 60000
	log.flush.scheduler.interval.ms = 9223372036854775807
	log.flush.start.offset.checkpoint.interval.ms = 60000
	log.index.interval.bytes = 4096
	log.index.size.max.bytes = 10485760
	log.initial.task.delay.ms = 30000
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	log.message.downconversion.enable = true
	log.message.format.version = 3.0-IV1
	log.message.timestamp.after.max.ms = 9223372036854775807
	log.message.timestamp.before.max.ms = 9223372036854775807
	log.message.timestamp.difference.max.ms = 9223372036854775807
	log.message.timestamp.type = CreateTime
	log.preallocate = false
	log.retention.bytes = -1
	log.retention.check.interval.ms = 300000
	log.retention.hours = 168
	log.retention.minutes = null
	log.retention.ms = null
	log.roll.hours = 168
	log.roll.jitter.hours = 0
	log.roll.jitter.ms = null
	log.roll.ms = null
	log.segment.bytes = 1073741824
	log.segment.delete.delay.ms = 60000
	max.connection.creation.rate = 2147483647
	max.connections = 2147483647
	max.connections.per.ip = 2147483647
	max.connections.per.ip.overrides = 
	max.incremental.fetch.session.cache.slots = 1000
	max.request.partition.size.limit = 2000
	message.max.bytes = 1048588
	metadata.log.dir = null
	metadata.log.max.record.bytes.between.snapshots = 20971520
	metadata.log.max.snapshot.interval.ms = 3600000
	metadata.log.segment.bytes = 1073741824
	metadata.log.segment.min.bytes = 8388608
	metadata.log.segment.ms = 604800000
	metadata.max.idle.interval.ms = 500
	metadata.max.retention.bytes = 104857600
	metadata.max.retention.ms = 604800000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	min.insync.replicas = 1
	node.id = 1
	num.io.threads = 8
	num.network.threads = 3
	num.partitions = 1
	num.recovery.threads.per.data.dir = 1
	num.replica.alter.log.dirs.threads = null
	num.replica.fetchers = 1
	offset.metadata.max.bytes = 4096
	offsets.commit.required.acks = -1
	offsets.commit.timeout.ms = 5000
	offsets.load.buffer.size = 5242880
	offsets.retention.check.interval.ms = 600000
	offsets.retention.minutes = 10080
	offsets.topic.compression.codec = 0
	offsets.topic.num.partitions = 50
	offsets.topic.replication.factor = 1
	offsets.topic.segment.bytes = 104857600
	password.encoder.cipher.algorithm = AES/CBC/PKCS5Padding
	password.encoder.iterations = 4096
	password.encoder.key.length = 128
	password.encoder.keyfactory.algorithm = null
	password.encoder.old.secret = null
	password.encoder.secret = null
	principal.builder.class = class org.apache.kafka.common.security.authenticator.DefaultKafkaPrincipalBuilder
	process.roles = [broker, controller]
	producer.id.expiration.check.interval.ms = 600000
	producer.id.expiration.ms = 86400000
	producer.purgatory.purge.interval.requests = 1000
	queued.max.request.bytes = -1
	queued.max.requests = 500
	quota.window.num = 11
	quota.window.size.seconds = 1
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
	replica.fetch.backoff.ms = 1000
	replica.fetch.max.bytes = 1048576
	replica.fetch.min.bytes = 1
	replica.fetch.response.max.bytes = 10485760
	replica.fetch.wait.max.ms = 500
	replica.high.watermark.checkpoint.interval.ms = 5000
	replica.lag.time.max.ms = 30000
	replica.selector.class = null
	replica.socket.receive.buffer.bytes = 65536
	replica.socket.timeout.ms = 30000
	replication.quota.window.num = 11
	replication.quota.window.size.seconds = 1
	request.timeout.ms = 30000
	reserved.broker.max.id = 1000
	sasl.client.callback.handler.class = null
	sasl.enabled.mechanisms = [GSSAPI]
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.principal.to.local.rules = [DEFAULT]
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism.controller.protocol = GSSAPI
	sasl.mechanism.inter.broker.protocol = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	sasl.server.callback.handler.class = null
	sasl.server.max.receive.size = 524288
	security.inter.broker.protocol = PLAINTEXT
	security.providers = null
	server.max.startup.time.ms = 9223372036854775807
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	socket.listen.backlog.size = 50
	socket.receive.buffer.bytes = 102400
	socket.request.max.bytes = 104857600
	socket.send.buffer.bytes = 102400
	ssl.allow.dn.changes = false
	ssl.allow.san.changes = false
	ssl.cipher.suites = []
	ssl.client.auth = none
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.principal.mapping.rules = DEFAULT
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	telemetry.max.bytes = 1048576
	transaction.abort.timed.out.transaction.cleanup.interval.ms = 10000
	transaction.max.timeout.ms = 900000
	transaction.partition.verification.enable = true
	transaction.remove.expired.transaction.cleanup.interval.ms = 3600000
	transaction.state.log.load.buffer.size = 5242880
	transaction.state.log.min.isr = 1
	transaction.state.log.num.partitions = 50
	transaction.state.log.replication.factor = 1
	transaction.state.log.segment.bytes = 104857600
	transactional.id.expiration.ms = 604800000
	unclean.leader.election.enable = false
	unstable.api.versions.enable = false
	unstable.feature.versions.enable = false
	zookeeper.clientCnxnSocket = null
	zookeeper.connect = null
	zookeeper.connection.timeout.ms = null
	zookeeper.max.in.flight.requests = 10
	zookeeper.metadata.migration.enable = false
	zookeeper.metadata.migration.min.batch.size = 200
	zookeeper.session.timeout.ms = 18000
	zookeeper.set.acl = false
	zookeeper.ssl.cipher.suites = null
	zookeeper.ssl.client.enable = false
	zookeeper.ssl.crl.enable = false
	zookeeper.ssl.enabled.protocols = null
	zookeeper.ssl.endpoint.identification.algorithm = HTTPS
	zookeeper.ssl.keystore.location = null
	zookeeper.ssl.keystore.password = null
	zookeeper.ssl.keystore.type = null
	zookeeper.ssl.ocsp.enable = false
	zookeeper.ssl.protocol = TLSv1.2
	zookeeper.ssl.truststore.location = null
	zookeeper.ssl.truststore.password = null
	zookeeper.ssl.truststore.type = null
 (kafka.server.KafkaConfig)
[2025-07-06 09:01:16,252] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:01:16,255] INFO [BrokerServer id=1] Waiting for the broker to be unfenced (kafka.server.BrokerServer)
[2025-07-06 09:01:16,291] INFO [BrokerLifecycleManager id=1] The broker has been unfenced. Transitioning from RECOVERY to RUNNING. (kafka.server.BrokerLifecycleManager)
[2025-07-06 09:01:16,291] INFO [BrokerServer id=1] Finished waiting for the broker to be unfenced (kafka.server.BrokerServer)
[2025-07-06 09:01:16,292] INFO authorizerStart completed for endpoint PLAINTEXT. Endpoint is now READY. (org.apache.kafka.server.network.EndpointReadyFutures)
[2025-07-06 09:01:16,292] INFO [SocketServer listenerType=BROKER, nodeId=1] Enabling request processing. (kafka.network.SocketServer)
[2025-07-06 09:01:16,293] INFO Awaiting socket connections on 0.0.0.0:9092. (kafka.network.DataPlaneAcceptor)
[2025-07-06 09:01:16,297] INFO [BrokerServer id=1] Waiting for all of the authorizer futures to be completed (kafka.server.BrokerServer)
[2025-07-06 09:01:16,297] INFO [BrokerServer id=1] Finished waiting for all of the authorizer futures to be completed (kafka.server.BrokerServer)
[2025-07-06 09:01:16,297] INFO [BrokerServer id=1] Waiting for all of the SocketServer Acceptors to be started (kafka.server.BrokerServer)
[2025-07-06 09:01:16,297] INFO [BrokerServer id=1] Finished waiting for all of the SocketServer Acceptors to be started (kafka.server.BrokerServer)
[2025-07-06 09:01:16,297] INFO [BrokerServer id=1] Transition from STARTING to STARTED (kafka.server.BrokerServer)
[2025-07-06 09:01:16,298] INFO Kafka version: 3.8.1 (org.apache.kafka.common.utils.AppInfoParser)
[2025-07-06 09:01:16,298] INFO Kafka commitId: 70d6ff42debf7e17 (org.apache.kafka.common.utils.AppInfoParser)
[2025-07-06 09:01:16,299] INFO Kafka startTimeMs: 1751763676298 (org.apache.kafka.common.utils.AppInfoParser)
[2025-07-06 09:01:16,300] INFO [KafkaRaftServer nodeId=1] Kafka Server started (kafka.server.KafkaRaftServer)
[2025-07-06 09:01:20,562] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /0:0:0:0:0:0:0:1 (channelId=0:0:0:0:0:0:0:1:9092-0:0:0:0:0:0:0:1:49254-0); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:01:20,572] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /0:0:0:0:0:0:0:1 (channelId=0:0:0:0:0:0:0:1:9092-0:0:0:0:0:0:0:1:49244-0); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:01:20,582] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /0:0:0:0:0:0:0:1 (channelId=0:0:0:0:0:0:0:1:9092-0:0:0:0:0:0:0:1:49270-0); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:01:21,640] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /0:0:0:0:0:0:0:1 (channelId=0:0:0:0:0:0:0:1:9092-0:0:0:0:0:0:0:1:49280-1); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:01:26,666] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /0:0:0:0:0:0:0:1 (channelId=0:0:0:0:0:0:0:1:9092-0:0:0:0:0:0:0:1:49292-1); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:01:26,677] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /0:0:0:0:0:0:0:1 (channelId=0:0:0:0:0:0:0:1:9092-0:0:0:0:0:0:0:1:56930-1); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:01:26,682] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /0:0:0:0:0:0:0:1 (channelId=0:0:0:0:0:0:0:1:9092-0:0:0:0:0:0:0:1:56940-2); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:01:38,532] INFO [ReplicaFetcherManager on broker 1] Removed fetcher for partitions Set(test-topic-0) (kafka.server.ReplicaFetcherManager)
[2025-07-06 09:01:38,555] INFO [LogLoader partition=test-topic-0, dir=/tmp/kraft-combined-logs] Loading producer state till offset 0 with message format version 2 (kafka.log.UnifiedLog$)
[2025-07-06 09:01:38,558] INFO Created log for partition test-topic-0 in /tmp/kraft-combined-logs/test-topic-0 with properties {} (kafka.log.LogManager)
[2025-07-06 09:01:38,560] INFO [Partition test-topic-0 broker=1] No checkpointed highwatermark is found for partition test-topic-0 (kafka.cluster.Partition)
[2025-07-06 09:01:38,561] INFO [Partition test-topic-0 broker=1] Log loaded for partition test-topic-0 with initial high watermark 0 (kafka.cluster.Partition)
[2025-07-06 09:01:56,724] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /0:0:0:0:0:0:0:1 (channelId=0:0:0:0:0:0:0:1:9092-0:0:0:0:0:0:0:1:54000-3); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:02:45,089] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /0:0:0:0:0:0:0:1 (channelId=0:0:0:0:0:0:0:1:9092-0:0:0:0:0:0:0:1:53986-3); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:02:45,094] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /0:0:0:0:0:0:0:1 (channelId=0:0:0:0:0:0:0:1:9092-0:0:0:0:0:0:0:1:51286-4); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:02:45,107] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /0:0:0:0:0:0:0:1 (channelId=0:0:0:0:0:0:0:1:9092-0:0:0:0:0:0:0:1:51296-4); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:02:47,560] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /0:0:0:0:0:0:0:1 (channelId=0:0:0:0:0:0:0:1:9092-0:0:0:0:0:0:0:1:51304-5); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:02:47,562] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /0:0:0:0:0:0:0:1 (channelId=0:0:0:0:0:0:0:1:9092-0:0:0:0:0:0:0:1:51298-5); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:02:47,569] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /0:0:0:0:0:0:0:1 (channelId=0:0:0:0:0:0:0:1:9092-0:0:0:0:0:0:0:1:51310-5); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:04:26,421] INFO Registered kafka:type=kafka.Log4jController MBean (kafka.utils.Log4jControllerRegistration$)
[2025-07-06 09:04:26,665] INFO Setting -D jdk.tls.rejectClientInitiatedRenegotiation=true to disable client-initiated TLS renegotiation (org.apache.zookeeper.common.X509Util)
[2025-07-06 09:04:26,668] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:04:26,904] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:04:26,911] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:04:26,935] INFO Registered signal handlers for TERM, INT, HUP (org.apache.kafka.common.utils.LoggingSignalHandler)
[2025-07-06 09:04:26,939] INFO [ControllerServer id=1] Starting controller (kafka.server.ControllerServer)
[2025-07-06 09:04:26,941] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:04:27,349] INFO Updated connection-accept-rate max connection creation rate to 2147483647 (kafka.network.ConnectionQuotas)
[2025-07-06 09:04:27,444] INFO [SocketServer listenerType=CONTROLLER, nodeId=1] Created data-plane acceptor and processors for endpoint : ListenerName(CONTROLLER) (kafka.network.SocketServer)
[2025-07-06 09:04:27,447] INFO CONTROLLER: resolved wildcard host to LAPTOP-H9435UC9. (org.apache.kafka.metadata.ListenerInfo)
[2025-07-06 09:04:27,451] INFO authorizerStart completed for endpoint CONTROLLER. Endpoint is now READY. (org.apache.kafka.server.network.EndpointReadyFutures)
[2025-07-06 09:04:27,452] INFO [SharedServer id=1] Starting SharedServer (kafka.server.SharedServer)
[2025-07-06 09:04:27,453] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:04:27,505] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Recovering unflushed segment 0. 0/1 recovered for __cluster_metadata-0. (kafka.log.LogLoader)
[2025-07-06 09:04:27,506] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Loading producer state till offset 0 with message format version 2 (kafka.log.UnifiedLog$)
[2025-07-06 09:04:27,506] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Reloading from producer snapshot and rebuilding producer state from offset 0 (kafka.log.UnifiedLog$)
[2025-07-06 09:04:27,507] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Producer state recovery took 0ms for snapshot load and 0ms for segment recovery from offset 0 (kafka.log.UnifiedLog$)
[2025-07-06 09:04:27,526] INFO [ProducerStateManager partition=__cluster_metadata-0] Wrote producer snapshot at offset 393 with 0 producer ids in 4 ms. (org.apache.kafka.storage.internals.log.ProducerStateManager)
[2025-07-06 09:04:27,532] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Loading producer state till offset 393 with message format version 2 (kafka.log.UnifiedLog$)
[2025-07-06 09:04:27,533] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Reloading from producer snapshot and rebuilding producer state from offset 393 (kafka.log.UnifiedLog$)
[2025-07-06 09:04:27,533] INFO [ProducerStateManager partition=__cluster_metadata-0] Loading producer state from snapshot file 'SnapshotFile(offset=393, file=/tmp/kraft-combined-logs/__cluster_metadata-0/00000000000000000393.snapshot)' (org.apache.kafka.storage.internals.log.ProducerStateManager)
[2025-07-06 09:04:27,534] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Producer state recovery took 1ms for snapshot load and 0ms for segment recovery from offset 393 (kafka.log.UnifiedLog$)
[2025-07-06 09:04:27,563] INFO Initialized snapshots with IDs SortedSet() from /tmp/kraft-combined-logs/__cluster_metadata-0 (kafka.raft.KafkaMetadataLog$)
[2025-07-06 09:04:27,580] INFO [raft-expiration-reaper]: Starting (kafka.raft.TimingWheelExpirationService$ExpiredOperationReaper)
[2025-07-06 09:04:27,594] INFO [RaftManager id=1] Reading KRaft snapshot and log as part of the initialization (org.apache.kafka.raft.KafkaRaftClient)
[2025-07-06 09:04:27,616] INFO [RaftManager id=1] Starting request manager with static voters: [localhost:9093 (id: 1 rack: null)] (org.apache.kafka.raft.KafkaRaftClient)
[2025-07-06 09:04:27,751] INFO [RaftManager id=1] Completed transition to ResignedState(localId=1, epoch=1, voters=[1], electionTimeoutMs=1657, unackedVoters=[], preferredSuccessors=[]) from null (org.apache.kafka.raft.QuorumState)
[2025-07-06 09:04:27,762] INFO [RaftManager id=1] Completed transition to CandidateState(localId=1, localDirectoryId=fByaQt4RR3XuGwEgMy431g,epoch=2, retries=1, voteStates={1=GRANTED}, highWatermark=Optional.empty, electionTimeoutMs=1691) from ResignedState(localId=1, epoch=1, voters=[1], electionTimeoutMs=1657, unackedVoters=[], preferredSuccessors=[]) (org.apache.kafka.raft.QuorumState)
[2025-07-06 09:04:27,773] INFO [RaftManager id=1] Completed transition to Leader(localId=1, epoch=2, epochStartOffset=393, highWatermark=Optional.empty, voterStates={1=ReplicaState(nodeId=1, endOffset=Optional.empty, lastFetchTimestamp=-1, lastCaughtUpTimestamp=-1, hasAcknowledgedLeader=true)}) from CandidateState(localId=1, localDirectoryId=fByaQt4RR3XuGwEgMy431g,epoch=2, retries=1, voteStates={1=GRANTED}, highWatermark=Optional.empty, electionTimeoutMs=1691) (org.apache.kafka.raft.QuorumState)
[2025-07-06 09:04:27,847] INFO [kafka-1-raft-outbound-request-thread]: Starting (org.apache.kafka.raft.KafkaNetworkChannel$SendThread)
[2025-07-06 09:04:27,848] INFO [kafka-1-raft-io-thread]: Starting (org.apache.kafka.raft.KafkaRaftClientDriver)
[2025-07-06 09:04:27,865] INFO [RaftManager id=1] High watermark set to LogOffsetMetadata(offset=394, metadata=Optional[(segmentBaseOffset=0,relativePositionInSegment=28428)]) for the first time for epoch 2 based on indexOfHw 0 and voters [ReplicaState(nodeId=1, endOffset=Optional[LogOffsetMetadata(offset=394, metadata=Optional[(segmentBaseOffset=0,relativePositionInSegment=28428)])], lastFetchTimestamp=-1, lastCaughtUpTimestamp=-1, hasAcknowledgedLeader=true)] (org.apache.kafka.raft.LeaderState)
[2025-07-06 09:04:27,869] INFO [MetadataLoader id=1] initializeNewPublishers: The loader is still catching up because we have loaded up to offset -1, but the high water mark is 394 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:27,870] INFO [ControllerServer id=1] Waiting for controller quorum voters future (kafka.server.ControllerServer)
[2025-07-06 09:04:27,870] INFO [ControllerServer id=1] Finished waiting for controller quorum voters future (kafka.server.ControllerServer)
[2025-07-06 09:04:27,878] INFO [RaftManager id=1] Registered the listener org.apache.kafka.image.loader.MetadataLoader@1835893973 (org.apache.kafka.raft.KafkaRaftClient)
[2025-07-06 09:04:27,882] INFO [MetadataLoader id=1] maybePublishMetadata(LOG_DELTA): The loader is still catching up because we have loaded up to offset 0, but the high water mark is 394 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:27,904] INFO [MetadataLoader id=1] maybePublishMetadata(LOG_DELTA): The loader finished catching up to the current high water mark of 394 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:27,908] INFO [RaftManager id=1] Registered the listener org.apache.kafka.controller.QuorumController$QuorumMetaLogListener@1904079272 (org.apache.kafka.raft.KafkaRaftClient)
[2025-07-06 09:04:27,909] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing SnapshotGenerator with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:27,916] INFO [controller-1-ThrottledChannelReaper-Fetch]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:04:27,917] INFO [controller-1-ThrottledChannelReaper-Produce]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:04:27,919] INFO [controller-1-ThrottledChannelReaper-Request]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:04:27,922] INFO [controller-1-ThrottledChannelReaper-ControllerMutation]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:04:28,023] INFO [ExpirationReaper-1-AlterAcls]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 09:04:28,036] INFO [ControllerServer id=1] Waiting for the controller metadata publishers to be installed (kafka.server.ControllerServer)
[2025-07-06 09:04:28,036] INFO [ControllerServer id=1] Finished waiting for the controller metadata publishers to be installed (kafka.server.ControllerServer)
[2025-07-06 09:04:28,036] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing KRaftMetadataCachePublisher with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,037] INFO [SocketServer listenerType=CONTROLLER, nodeId=1] Enabling request processing. (kafka.network.SocketServer)
[2025-07-06 09:04:28,037] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing FeaturesPublisher with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,037] INFO [ControllerServer id=1] Loaded new metadata Features(metadataVersion=3.8-IV0, finalizedFeatures={metadata.version=20}, finalizedFeaturesEpoch=393). (org.apache.kafka.metadata.publisher.FeaturesPublisher)
[2025-07-06 09:04:28,037] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing ControllerRegistrationsPublisher with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,037] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing ControllerRegistrationManager with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,038] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing DynamicConfigPublisher controller id=1 with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,038] INFO [ControllerRegistrationManager id=1 incarnation=DNidUPKcTBaMP1zkmouFAg] Found registration for dDkvVYmZRpiis_Rf1Fcrrg instead of our incarnation. (kafka.server.ControllerRegistrationManager)
[2025-07-06 09:04:28,038] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing DynamicClientQuotaPublisher controller id=1 with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,039] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing ScramPublisher controller id=1 with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,040] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing DelegationTokenPublisher controller id=1 with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,040] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing ControllerMetadataMetricsPublisher with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,041] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing AclPublisher controller id=1 with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,042] ERROR Unable to start acceptor for ListenerName(CONTROLLER) (kafka.network.DataPlaneAcceptor)
org.apache.kafka.common.KafkaException: Socket server failed to bind to 0.0.0.0:9093: Address already in use.
	at kafka.network.Acceptor.openServerSocket(SocketServer.scala:737)
	at kafka.network.Acceptor.liftedTree1$1(SocketServer.scala:633)
	at kafka.network.Acceptor.start(SocketServer.scala:628)
	at kafka.network.SocketServer.$anonfun$enableRequestProcessing$2(SocketServer.scala:224)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:774)
	at java.util.concurrent.CompletableFuture.uniWhenCompleteStage(CompletableFuture.java:792)
	at java.util.concurrent.CompletableFuture.whenComplete(CompletableFuture.java:2153)
	at kafka.network.SocketServer.chainAcceptorFuture$1(SocketServer.scala:217)
	at kafka.network.SocketServer.$anonfun$enableRequestProcessing$5(SocketServer.scala:231)
	at java.util.concurrent.ConcurrentHashMap$ValuesView.forEach(ConcurrentHashMap.java:4705)
	at kafka.network.SocketServer.enableRequestProcessing(SocketServer.scala:231)
	at kafka.server.ControllerServer.startup(ControllerServer.scala:434)
	at kafka.server.KafkaRaftServer.$anonfun$startup$1(KafkaRaftServer.scala:96)
	at kafka.server.KafkaRaftServer.$anonfun$startup$1$adapted(KafkaRaftServer.scala:96)
	at scala.Option.foreach(Option.scala:437)
	at kafka.server.KafkaRaftServer.startup(KafkaRaftServer.scala:96)
	at kafka.Kafka$.main(Kafka.scala:112)
	at kafka.Kafka.main(Kafka.scala)
Caused by: java.net.BindException: Address already in use
	at sun.nio.ch.Net.bind0(Native Method)
	at sun.nio.ch.Net.bind(Net.java:461)
	at sun.nio.ch.Net.bind(Net.java:453)
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:222)
	at sun.nio.ch.ServerSocketAdaptor.bind(ServerSocketAdaptor.java:85)
	at kafka.network.Acceptor.openServerSocket(SocketServer.scala:732)
	... 17 more
[2025-07-06 09:04:28,056] INFO [controller-1-to-controller-registration-channel-manager]: Starting (kafka.server.NodeToControllerRequestThread)
[2025-07-06 09:04:28,057] INFO [ControllerServer id=1] Waiting for all of the authorizer futures to be completed (kafka.server.ControllerServer)
[2025-07-06 09:04:28,057] INFO [ControllerRegistrationManager id=1 incarnation=DNidUPKcTBaMP1zkmouFAg] initialized channel manager. (kafka.server.ControllerRegistrationManager)
[2025-07-06 09:04:28,057] INFO [ControllerServer id=1] Finished waiting for all of the authorizer futures to be completed (kafka.server.ControllerServer)
[2025-07-06 09:04:28,057] INFO [controller-1-to-controller-registration-channel-manager]: Recorded new KRaft controller, from now on will use node localhost:9093 (id: 1 rack: null) (kafka.server.NodeToControllerRequestThread)
[2025-07-06 09:04:28,058] INFO [ControllerServer id=1] Waiting for all of the SocketServer Acceptors to be started (kafka.server.ControllerServer)
[2025-07-06 09:04:28,058] ERROR [ControllerServer id=1] Received a fatal error while waiting for all of the SocketServer Acceptors to be started (kafka.server.ControllerServer)
java.lang.RuntimeException: Unable to start acceptor for ListenerName(CONTROLLER)
	at kafka.network.Acceptor.liftedTree1$1(SocketServer.scala:648)
	at kafka.network.Acceptor.start(SocketServer.scala:628)
	at kafka.network.SocketServer.$anonfun$enableRequestProcessing$2(SocketServer.scala:224)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:774)
	at java.util.concurrent.CompletableFuture.uniWhenCompleteStage(CompletableFuture.java:792)
	at java.util.concurrent.CompletableFuture.whenComplete(CompletableFuture.java:2153)
	at kafka.network.SocketServer.chainAcceptorFuture$1(SocketServer.scala:217)
	at kafka.network.SocketServer.$anonfun$enableRequestProcessing$5(SocketServer.scala:231)
	at java.util.concurrent.ConcurrentHashMap$ValuesView.forEach(ConcurrentHashMap.java:4705)
	at kafka.network.SocketServer.enableRequestProcessing(SocketServer.scala:231)
	at kafka.server.ControllerServer.startup(ControllerServer.scala:434)
	at kafka.server.KafkaRaftServer.$anonfun$startup$1(KafkaRaftServer.scala:96)
	at kafka.server.KafkaRaftServer.$anonfun$startup$1$adapted(KafkaRaftServer.scala:96)
	at scala.Option.foreach(Option.scala:437)
	at kafka.server.KafkaRaftServer.startup(KafkaRaftServer.scala:96)
	at kafka.Kafka$.main(Kafka.scala:112)
	at kafka.Kafka.main(Kafka.scala)
Caused by: org.apache.kafka.common.KafkaException: Socket server failed to bind to 0.0.0.0:9093: Address already in use.
	at kafka.network.Acceptor.openServerSocket(SocketServer.scala:737)
	at kafka.network.Acceptor.liftedTree1$1(SocketServer.scala:633)
	... 16 more
Caused by: java.net.BindException: Address already in use
	at sun.nio.ch.Net.bind0(Native Method)
	at sun.nio.ch.Net.bind(Net.java:461)
	at sun.nio.ch.Net.bind(Net.java:453)
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:222)
	at sun.nio.ch.ServerSocketAdaptor.bind(ServerSocketAdaptor.java:85)
	at kafka.network.Acceptor.openServerSocket(SocketServer.scala:732)
	... 17 more
[2025-07-06 09:04:28,059] INFO [ControllerRegistrationManager id=1 incarnation=DNidUPKcTBaMP1zkmouFAg] sendControllerRegistration: attempting to send ControllerRegistrationRequestData(controllerId=1, incarnationId=DNidUPKcTBaMP1zkmouFAg, zkMigrationReady=false, listeners=[Listener(name='CONTROLLER', host='LAPTOP-H9435UC9.', port=9093, securityProtocol=0)], features=[Feature(name='metadata.version', minSupportedVersion=1, maxSupportedVersion=20)]) (kafka.server.ControllerRegistrationManager)
[2025-07-06 09:04:28,059] ERROR Encountered fatal fault: caught exception (org.apache.kafka.server.fault.ProcessTerminatingFaultHandler)
java.lang.RuntimeException: Received a fatal error while waiting for all of the SocketServer Acceptors to be started
	at org.apache.kafka.server.util.FutureUtils.waitWithLogging(FutureUtils.java:72)
	at kafka.server.ControllerServer.startup(ControllerServer.scala:459)
	at kafka.server.KafkaRaftServer.$anonfun$startup$1(KafkaRaftServer.scala:96)
	at kafka.server.KafkaRaftServer.$anonfun$startup$1$adapted(KafkaRaftServer.scala:96)
	at scala.Option.foreach(Option.scala:437)
	at kafka.server.KafkaRaftServer.startup(KafkaRaftServer.scala:96)
	at kafka.Kafka$.main(Kafka.scala:112)
	at kafka.Kafka.main(Kafka.scala)
Caused by: java.lang.RuntimeException: Unable to start acceptor for ListenerName(CONTROLLER)
	at kafka.network.Acceptor.liftedTree1$1(SocketServer.scala:648)
	at kafka.network.Acceptor.start(SocketServer.scala:628)
	at kafka.network.SocketServer.$anonfun$enableRequestProcessing$2(SocketServer.scala:224)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:774)
	at java.util.concurrent.CompletableFuture.uniWhenCompleteStage(CompletableFuture.java:792)
	at java.util.concurrent.CompletableFuture.whenComplete(CompletableFuture.java:2153)
	at kafka.network.SocketServer.chainAcceptorFuture$1(SocketServer.scala:217)
	at kafka.network.SocketServer.$anonfun$enableRequestProcessing$5(SocketServer.scala:231)
	at java.util.concurrent.ConcurrentHashMap$ValuesView.forEach(ConcurrentHashMap.java:4705)
	at kafka.network.SocketServer.enableRequestProcessing(SocketServer.scala:231)
	at kafka.server.ControllerServer.startup(ControllerServer.scala:434)
	... 6 more
Caused by: org.apache.kafka.common.KafkaException: Socket server failed to bind to 0.0.0.0:9093: Address already in use.
	at kafka.network.Acceptor.openServerSocket(SocketServer.scala:737)
	at kafka.network.Acceptor.liftedTree1$1(SocketServer.scala:633)
	... 16 more
Caused by: java.net.BindException: Address already in use
	at sun.nio.ch.Net.bind0(Native Method)
	at sun.nio.ch.Net.bind(Net.java:461)
	at sun.nio.ch.Net.bind(Net.java:453)
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:222)
	at sun.nio.ch.ServerSocketAdaptor.bind(ServerSocketAdaptor.java:85)
	at kafka.network.Acceptor.openServerSocket(SocketServer.scala:732)
	... 17 more
[2025-07-06 09:05:26,978] WARN [SocketServer listenerType=CONTROLLER, nodeId=1] Unexpected error from /*********** (channelId=*************:9093-***********:53643-1); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:05:28,255] WARN [SocketServer listenerType=CONTROLLER, nodeId=1] Unexpected error from /*********** (channelId=*************:9093-***********:53644-1); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:05:28,257] WARN [SocketServer listenerType=CONTROLLER, nodeId=1] Unexpected error from /*********** (channelId=*************:9093-***********:53648-1); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:05:28,260] WARN [SocketServer listenerType=CONTROLLER, nodeId=1] Unexpected error from /*********** (channelId=*************:9093-***********:53649-2); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:05:33,300] WARN [SocketServer listenerType=CONTROLLER, nodeId=1] Unexpected error from /*********** (channelId=*************:9093-***********:53689-2); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:05:33,302] WARN [SocketServer listenerType=CONTROLLER, nodeId=1] Unexpected error from /*********** (channelId=*************:9093-***********:53690-2); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:05:33,305] WARN [SocketServer listenerType=CONTROLLER, nodeId=1] Unexpected error from /*********** (channelId=*************:9093-***********:53691-3); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:06:03,349] WARN [SocketServer listenerType=CONTROLLER, nodeId=1] Unexpected error from /*********** (channelId=*************:9093-***********:53796-3); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:07:03,385] WARN [SocketServer listenerType=CONTROLLER, nodeId=1] Unexpected error from /*********** (channelId=*************:9093-***********:53962-4); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:07:03,387] WARN [SocketServer listenerType=CONTROLLER, nodeId=1] Unexpected error from /*********** (channelId=*************:9093-***********:53961-4); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:07:03,390] WARN [SocketServer listenerType=CONTROLLER, nodeId=1] Unexpected error from /*********** (channelId=*************:9093-***********:53963-4); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:07:28,847] WARN [SocketServer listenerType=CONTROLLER, nodeId=1] Unexpected error from /127.0.0.1 (channelId=127.0.0.1:9093-127.0.0.1:40412-5); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
[2025-07-06 09:08:09,970] WARN [SocketServer listenerType=BROKER, nodeId=1] Unexpected error from /127.0.0.1 (channelId=127.0.0.1:9092-127.0.0.1:50490-6); closing connection (org.apache.kafka.common.network.Selector)
org.apache.kafka.common.network.InvalidReceiveException: Invalid receive (size = 1195725856 larger than 104857600)
	at org.apache.kafka.common.network.NetworkReceive.readFrom(NetworkReceive.java:94)
	at org.apache.kafka.common.network.KafkaChannel.receive(KafkaChannel.java:462)
	at org.apache.kafka.common.network.KafkaChannel.read(KafkaChannel.java:412)
	at org.apache.kafka.common.network.Selector.attemptRead(Selector.java:678)
	at org.apache.kafka.common.network.Selector.pollSelectionKeys(Selector.java:580)
	at org.apache.kafka.common.network.Selector.poll(Selector.java:485)
	at kafka.network.Processor.poll(SocketServer.scala:1111)
	at kafka.network.Processor.run(SocketServer.scala:1015)
	at java.lang.Thread.run(Thread.java:750)
