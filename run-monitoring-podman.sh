#!/bin/bash

# 使用Podman运行Kafka Order Service监控栈
# 适用于Windows Podman环境

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🚀 使用Podman启动Kafka Order Service监控栈${NC}"

# 创建网络
echo -e "${GREEN}📡 创建Podman网络...${NC}"
podman network create kafka-network 2>/dev/null || echo "网络已存在"

# 启动Prometheus
echo -e "${GREEN}📊 启动Prometheus...${NC}"
podman run -d \
  --name prometheus \
  --network kafka-network \
  -p 9090:9090 \
  -v "$(pwd)/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro" \
  -v prometheus-data:/prometheus \
  --restart unless-stopped \
  prom/prometheus:latest \
  --config.file=/etc/prometheus/prometheus.yml \
  --storage.tsdb.path=/prometheus \
  --web.console.libraries=/etc/prometheus/console_libraries \
  --web.console.templates=/etc/prometheus/consoles \
  --storage.tsdb.retention.time=200h \
  --web.enable-lifecycle

# 启动Grafana
echo -e "${GREEN}📈 启动Grafana...${NC}"
podman run -d \
  --name grafana \
  --network kafka-network \
  -p 3000:3000 \
  -e GF_SECURITY_ADMIN_PASSWORD=admin \
  -v grafana-data:/var/lib/grafana \
  -v "$(pwd)/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro" \
  -v "$(pwd)/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro" \
  --restart unless-stopped \
  grafana/grafana:latest

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 10

# 检查服务状态
echo -e "${GREEN}✅ 检查服务状态...${NC}"
echo "Prometheus: $(podman ps --filter name=prometheus --format '{{.Status}}')"
echo "Grafana: $(podman ps --filter name=grafana --format '{{.Status}}')"

echo ""
echo -e "${BLUE}🎉 监控栈启动完成！${NC}"
echo ""
echo -e "${GREEN}📊 访问地址:${NC}"
echo "• Prometheus: http://localhost:9090"
echo "• Grafana: http://localhost:3000 (admin/admin)"
echo "• 应用监控: http://localhost:8080/actuator/prometheus"
echo ""
echo -e "${YELLOW}💡 提示:${NC}"
echo "• 在Grafana中添加Prometheus数据源: http://prometheus:9090"
echo "• 导入Kafka监控仪表板"
echo "• 使用 './stop-monitoring-podman.sh' 停止服务"
