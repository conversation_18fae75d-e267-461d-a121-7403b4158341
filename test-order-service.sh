#!/bin/bash

# Kafka Order Service 测试脚本
# 用于验证系统功能和性能

set -e

# 配置
SERVICE_URL="http://localhost:8080"
KAFKA_BOOTSTRAP_SERVERS="localhost:9092"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    log_info "检查服务状态..."
    
    if curl -s -f "$SERVICE_URL/actuator/health" > /dev/null; then
        log_info "✅ 服务运行正常"
    else
        log_error "❌ 服务未运行或不健康"
        exit 1
    fi
}

# 检查Kafka连接
check_kafka() {
    log_info "检查Kafka连接..."
    
    # 检查Kafka是否运行
    if nc -z localhost 9092; then
        log_info "✅ Kafka连接正常"
    else
        log_error "❌ 无法连接到Kafka"
        exit 1
    fi
}

# 创建测试主题
create_topics() {
    log_info "创建测试主题..."
    
    # 检查是否有kafka命令行工具
    if command -v kafka-topics.sh &> /dev/null; then
        KAFKA_CMD="kafka-topics.sh"
    elif [ -f "./kafka/bin/kafka-topics.sh" ]; then
        KAFKA_CMD="./kafka/bin/kafka-topics.sh"
    else
        log_warn "未找到Kafka命令行工具，跳过主题创建"
        return
    fi
    
    # 创建主题
    topics=("order-events" "order-status-updates" "order-dlq")
    
    for topic in "${topics[@]}"; do
        if $KAFKA_CMD --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --list | grep -q "^$topic$"; then
            log_info "主题 $topic 已存在"
        else
            $KAFKA_CMD --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS \
                --create --topic $topic \
                --partitions 3 --replication-factor 1
            log_info "✅ 创建主题: $topic"
        fi
    done
}

# 测试健康检查
test_health() {
    log_info "测试健康检查..."
    
    response=$(curl -s "$SERVICE_URL/actuator/health")
    if echo "$response" | grep -q '"status":"UP"'; then
        log_info "✅ 健康检查通过"
    else
        log_error "❌ 健康检查失败: $response"
        return 1
    fi
}

# 测试创建单个订单
test_create_order() {
    log_info "测试创建单个订单..."
    
    order_data='{
        "user_id": "test-user-001",
        "items": [
            {
                "product_id": "prod-001",
                "product_name": "测试商品",
                "quantity": 2,
                "unit_price": 99.99
            }
        ],
        "shipping_address": {
            "recipient_name": "张三",
            "phone": "13800138000",
            "detail_address": "北京市朝阳区测试街道123号"
        },
        "payment_method": "ALIPAY",
        "source": "TEST"
    }'
    
    response=$(curl -s -X POST "$SERVICE_URL/api/v1/orders" \
        -H "Content-Type: application/json" \
        -d "$order_data")
    
    if echo "$response" | grep -q '"order_id"'; then
        order_id=$(echo "$response" | grep -o '"order_id":"[^"]*"' | cut -d'"' -f4)
        log_info "✅ 订单创建成功: $order_id"
        echo "$order_id"
    else
        log_error "❌ 订单创建失败: $response"
        return 1
    fi
}

# 测试更新订单状态
test_update_order_status() {
    local order_id=$1
    log_info "测试更新订单状态..."
    
    response=$(curl -s -X PUT "$SERVICE_URL/api/v1/orders/$order_id/status?status=PAID&remarks=测试支付")
    
    if echo "$response" | grep -q '"status":"PAID"'; then
        log_info "✅ 订单状态更新成功"
    else
        log_error "❌ 订单状态更新失败: $response"
        return 1
    fi
}

# 测试批量创建订单
test_batch_orders() {
    log_info "测试批量创建订单..."
    
    local count=10
    response=$(curl -s -X POST "$SERVICE_URL/api/v1/orders/batch?count=$count&userId=batch-test-user")
    
    if echo "$response" | grep -q "成功创建.*$count.*批量订单"; then
        log_info "✅ 批量订单创建成功: $count 个订单"
    else
        log_error "❌ 批量订单创建失败: $response"
        return 1
    fi
}

# 性能测试
performance_test() {
    log_info "开始性能测试..."
    
    local total_orders=1000
    local concurrent_requests=10
    local orders_per_request=$((total_orders / concurrent_requests))
    
    log_info "总订单数: $total_orders, 并发请求: $concurrent_requests"
    
    start_time=$(date +%s)
    
    # 并发发送请求
    for i in $(seq 1 $concurrent_requests); do
        curl -s -X POST "$SERVICE_URL/api/v1/orders/batch?count=$orders_per_request&userId=perf-test-$i" &
    done
    
    # 等待所有请求完成
    wait
    
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    if [ $duration -gt 0 ]; then
        tps=$((total_orders / duration))
        log_info "✅ 性能测试完成"
        log_info "   总订单数: $total_orders"
        log_info "   耗时: ${duration}秒"
        log_info "   平均TPS: $tps"
    else
        log_warn "测试完成太快，无法计算准确的TPS"
    fi
}

# 检查监控指标
check_metrics() {
    log_info "检查监控指标..."
    
    metrics_response=$(curl -s "$SERVICE_URL/actuator/prometheus")
    
    if echo "$metrics_response" | grep -q "kafka_order_sent_total"; then
        sent_total=$(echo "$metrics_response" | grep "kafka_order_sent_total" | tail -1 | awk '{print $2}')
        log_info "✅ 发送订单总数: $sent_total"
    fi
    
    if echo "$metrics_response" | grep -q "kafka_order_received_total"; then
        received_total=$(echo "$metrics_response" | grep "kafka_order_received_total" | tail -1 | awk '{print $2}')
        log_info "✅ 接收订单总数: $received_total"
    fi
}

# 清理测试数据
cleanup() {
    log_info "清理测试数据..."
    # 这里可以添加清理逻辑，比如删除测试订单等
    log_info "✅ 清理完成"
}

# 主函数
main() {
    log_info "开始Kafka Order Service测试"
    log_info "================================"
    
    # 基础检查
    check_service
    check_kafka
    create_topics
    
    # 功能测试
    test_health
    order_id=$(test_create_order)
    if [ -n "$order_id" ]; then
        test_update_order_status "$order_id"
    fi
    test_batch_orders
    
    # 等待消息处理
    log_info "等待消息处理..."
    sleep 5
    
    # 性能测试
    if [ "$1" = "--performance" ]; then
        performance_test
    fi
    
    # 检查指标
    check_metrics
    
    # 清理
    if [ "$1" = "--cleanup" ]; then
        cleanup
    fi
    
    log_info "================================"
    log_info "✅ 所有测试完成"
}

# 显示帮助信息
show_help() {
    echo "Kafka Order Service 测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --performance    运行性能测试"
    echo "  --cleanup        测试后清理数据"
    echo "  --help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 运行基础功能测试"
    echo "  $0 --performance      # 运行包含性能测试的完整测试"
    echo "  $0 --cleanup          # 运行测试并清理数据"
}

# 参数处理
case "$1" in
    --help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
