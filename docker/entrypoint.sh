#!/bin/bash

# 等待Kafka可用
wait_for_kafka() {
    echo "Waiting for Kafka to be available..."
    
    IFS=',' read -ra KAFKA_HOSTS <<< "$KAFKA_BOOTSTRAP_SERVERS"
    for host in "${KAFKA_HOSTS[@]}"; do
        IFS=':' read -ra HOST_PORT <<< "$host"
        kafka_host=${HOST_PORT[0]}
        kafka_port=${HOST_PORT[1]:-9092}
        
        echo "Checking Kafka at $kafka_host:$kafka_port"
        
        while ! nc -z "$kafka_host" "$kafka_port"; do
            echo "Kafka is not available at $kafka_host:$kafka_port. Waiting..."
            sleep 5
        done
        
        echo "Kafka is available at $kafka_host:$kafka_port"
    done
    
    echo "All Kafka brokers are available!"
}

# 如果设置了KAFKA_BOOTSTRAP_SERVERS，等待Kafka可用
if [ -n "$KAFKA_BOOTSTRAP_SERVERS" ]; then
    wait_for_kafka
fi

# 设置默认的JVM参数
if [ -z "$JAVA_OPTS" ]; then
    JAVA_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
fi

# 添加生产环境的JVM参数
if [ "$SPRING_PROFILES_ACTIVE" = "prod" ]; then
    JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/kafka-order-service/"
    JAVA_OPTS="$JAVA_OPTS -XX:+UseStringDeduplication"
    JAVA_OPTS="$JAVA_OPTS -XX:+UnlockExperimentalVMOptions"
    JAVA_OPTS="$JAVA_OPTS -Djava.security.egd=file:/dev/./urandom"
fi

echo "Starting Kafka Order Service with JAVA_OPTS: $JAVA_OPTS"

# 启动应用
exec java $JAVA_OPTS -jar app.jar
