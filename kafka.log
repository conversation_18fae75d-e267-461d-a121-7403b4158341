[2025-07-06 09:04:26,421] INFO Registered kafka:type=kafka.Log4jController MBean (kafka.utils.Log4jControllerRegistration$)
[2025-07-06 09:04:26,665] INFO Setting -D jdk.tls.rejectClientInitiatedRenegotiation=true to disable client-initiated TLS renegotiation (org.apache.zookeeper.common.X509Util)
[2025-07-06 09:04:26,668] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:04:26,904] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:04:26,911] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:04:26,935] INFO Registered signal handlers for TERM, INT, HUP (org.apache.kafka.common.utils.LoggingSignalHandler)
[2025-07-06 09:04:26,939] INFO [ControllerServer id=1] Starting controller (kafka.server.ControllerServer)
[2025-07-06 09:04:26,941] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:04:27,349] INFO Updated connection-accept-rate max connection creation rate to 2147483647 (kafka.network.ConnectionQuotas)
[2025-07-06 09:04:27,444] INFO [SocketServer listenerType=CONTROLLER, nodeId=1] Created data-plane acceptor and processors for endpoint : ListenerName(CONTROLLER) (kafka.network.SocketServer)
[2025-07-06 09:04:27,447] INFO CONTROLLER: resolved wildcard host to LAPTOP-H9435UC9. (org.apache.kafka.metadata.ListenerInfo)
[2025-07-06 09:04:27,451] INFO authorizerStart completed for endpoint CONTROLLER. Endpoint is now READY. (org.apache.kafka.server.network.EndpointReadyFutures)
[2025-07-06 09:04:27,452] INFO [SharedServer id=1] Starting SharedServer (kafka.server.SharedServer)
[2025-07-06 09:04:27,453] INFO RemoteLogManagerConfig values: 
	log.local.retention.bytes = -2
	log.local.retention.ms = -2
	remote.fetch.max.wait.ms = 500
	remote.log.index.file.cache.total.size.bytes = 1073741824
	remote.log.manager.copier.thread.pool.size = 10
	remote.log.manager.copy.max.bytes.per.second = 9223372036854775807
	remote.log.manager.copy.quota.window.num = 11
	remote.log.manager.copy.quota.window.size.seconds = 1
	remote.log.manager.expiration.thread.pool.size = 10
	remote.log.manager.fetch.max.bytes.per.second = 9223372036854775807
	remote.log.manager.fetch.quota.window.num = 11
	remote.log.manager.fetch.quota.window.size.seconds = 1
	remote.log.manager.task.interval.ms = 30000
	remote.log.manager.task.retry.backoff.max.ms = 30000
	remote.log.manager.task.retry.backoff.ms = 500
	remote.log.manager.task.retry.jitter = 0.2
	remote.log.manager.thread.pool.size = 10
	remote.log.metadata.custom.metadata.max.bytes = 128
	remote.log.metadata.manager.class.name = org.apache.kafka.server.log.remote.metadata.storage.TopicBasedRemoteLogMetadataManager
	remote.log.metadata.manager.class.path = null
	remote.log.metadata.manager.impl.prefix = rlmm.config.
	remote.log.metadata.manager.listener.name = null
	remote.log.reader.max.pending.tasks = 100
	remote.log.reader.threads = 10
	remote.log.storage.manager.class.name = null
	remote.log.storage.manager.class.path = null
	remote.log.storage.manager.impl.prefix = rsm.config.
	remote.log.storage.system.enable = false
 (org.apache.kafka.server.log.remote.storage.RemoteLogManagerConfig)
[2025-07-06 09:04:27,505] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Recovering unflushed segment 0. 0/1 recovered for __cluster_metadata-0. (kafka.log.LogLoader)
[2025-07-06 09:04:27,506] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Loading producer state till offset 0 with message format version 2 (kafka.log.UnifiedLog$)
[2025-07-06 09:04:27,506] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Reloading from producer snapshot and rebuilding producer state from offset 0 (kafka.log.UnifiedLog$)
[2025-07-06 09:04:27,507] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Producer state recovery took 0ms for snapshot load and 0ms for segment recovery from offset 0 (kafka.log.UnifiedLog$)
[2025-07-06 09:04:27,526] INFO [ProducerStateManager partition=__cluster_metadata-0] Wrote producer snapshot at offset 393 with 0 producer ids in 4 ms. (org.apache.kafka.storage.internals.log.ProducerStateManager)
[2025-07-06 09:04:27,532] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Loading producer state till offset 393 with message format version 2 (kafka.log.UnifiedLog$)
[2025-07-06 09:04:27,533] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Reloading from producer snapshot and rebuilding producer state from offset 393 (kafka.log.UnifiedLog$)
[2025-07-06 09:04:27,533] INFO [ProducerStateManager partition=__cluster_metadata-0] Loading producer state from snapshot file 'SnapshotFile(offset=393, file=/tmp/kraft-combined-logs/__cluster_metadata-0/00000000000000000393.snapshot)' (org.apache.kafka.storage.internals.log.ProducerStateManager)
[2025-07-06 09:04:27,534] INFO [LogLoader partition=__cluster_metadata-0, dir=/tmp/kraft-combined-logs] Producer state recovery took 1ms for snapshot load and 0ms for segment recovery from offset 393 (kafka.log.UnifiedLog$)
[2025-07-06 09:04:27,563] INFO Initialized snapshots with IDs SortedSet() from /tmp/kraft-combined-logs/__cluster_metadata-0 (kafka.raft.KafkaMetadataLog$)
[2025-07-06 09:04:27,580] INFO [raft-expiration-reaper]: Starting (kafka.raft.TimingWheelExpirationService$ExpiredOperationReaper)
[2025-07-06 09:04:27,594] INFO [RaftManager id=1] Reading KRaft snapshot and log as part of the initialization (org.apache.kafka.raft.KafkaRaftClient)
[2025-07-06 09:04:27,616] INFO [RaftManager id=1] Starting request manager with static voters: [localhost:9093 (id: 1 rack: null)] (org.apache.kafka.raft.KafkaRaftClient)
[2025-07-06 09:04:27,751] INFO [RaftManager id=1] Completed transition to ResignedState(localId=1, epoch=1, voters=[1], electionTimeoutMs=1657, unackedVoters=[], preferredSuccessors=[]) from null (org.apache.kafka.raft.QuorumState)
[2025-07-06 09:04:27,762] INFO [RaftManager id=1] Completed transition to CandidateState(localId=1, localDirectoryId=fByaQt4RR3XuGwEgMy431g,epoch=2, retries=1, voteStates={1=GRANTED}, highWatermark=Optional.empty, electionTimeoutMs=1691) from ResignedState(localId=1, epoch=1, voters=[1], electionTimeoutMs=1657, unackedVoters=[], preferredSuccessors=[]) (org.apache.kafka.raft.QuorumState)
[2025-07-06 09:04:27,773] INFO [RaftManager id=1] Completed transition to Leader(localId=1, epoch=2, epochStartOffset=393, highWatermark=Optional.empty, voterStates={1=ReplicaState(nodeId=1, endOffset=Optional.empty, lastFetchTimestamp=-1, lastCaughtUpTimestamp=-1, hasAcknowledgedLeader=true)}) from CandidateState(localId=1, localDirectoryId=fByaQt4RR3XuGwEgMy431g,epoch=2, retries=1, voteStates={1=GRANTED}, highWatermark=Optional.empty, electionTimeoutMs=1691) (org.apache.kafka.raft.QuorumState)
[2025-07-06 09:04:27,847] INFO [kafka-1-raft-outbound-request-thread]: Starting (org.apache.kafka.raft.KafkaNetworkChannel$SendThread)
[2025-07-06 09:04:27,848] INFO [kafka-1-raft-io-thread]: Starting (org.apache.kafka.raft.KafkaRaftClientDriver)
[2025-07-06 09:04:27,865] INFO [RaftManager id=1] High watermark set to LogOffsetMetadata(offset=394, metadata=Optional[(segmentBaseOffset=0,relativePositionInSegment=28428)]) for the first time for epoch 2 based on indexOfHw 0 and voters [ReplicaState(nodeId=1, endOffset=Optional[LogOffsetMetadata(offset=394, metadata=Optional[(segmentBaseOffset=0,relativePositionInSegment=28428)])], lastFetchTimestamp=-1, lastCaughtUpTimestamp=-1, hasAcknowledgedLeader=true)] (org.apache.kafka.raft.LeaderState)
[2025-07-06 09:04:27,869] INFO [MetadataLoader id=1] initializeNewPublishers: The loader is still catching up because we have loaded up to offset -1, but the high water mark is 394 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:27,870] INFO [ControllerServer id=1] Waiting for controller quorum voters future (kafka.server.ControllerServer)
[2025-07-06 09:04:27,870] INFO [ControllerServer id=1] Finished waiting for controller quorum voters future (kafka.server.ControllerServer)
[2025-07-06 09:04:27,878] INFO [RaftManager id=1] Registered the listener org.apache.kafka.image.loader.MetadataLoader@1835893973 (org.apache.kafka.raft.KafkaRaftClient)
[2025-07-06 09:04:27,882] INFO [MetadataLoader id=1] maybePublishMetadata(LOG_DELTA): The loader is still catching up because we have loaded up to offset 0, but the high water mark is 394 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:27,904] INFO [MetadataLoader id=1] maybePublishMetadata(LOG_DELTA): The loader finished catching up to the current high water mark of 394 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:27,908] INFO [RaftManager id=1] Registered the listener org.apache.kafka.controller.QuorumController$QuorumMetaLogListener@1904079272 (org.apache.kafka.raft.KafkaRaftClient)
[2025-07-06 09:04:27,909] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing SnapshotGenerator with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:27,916] INFO [controller-1-ThrottledChannelReaper-Fetch]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:04:27,917] INFO [controller-1-ThrottledChannelReaper-Produce]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:04:27,919] INFO [controller-1-ThrottledChannelReaper-Request]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:04:27,922] INFO [controller-1-ThrottledChannelReaper-ControllerMutation]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-07-06 09:04:28,023] INFO [ExpirationReaper-1-AlterAcls]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-07-06 09:04:28,036] INFO [ControllerServer id=1] Waiting for the controller metadata publishers to be installed (kafka.server.ControllerServer)
[2025-07-06 09:04:28,036] INFO [ControllerServer id=1] Finished waiting for the controller metadata publishers to be installed (kafka.server.ControllerServer)
[2025-07-06 09:04:28,036] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing KRaftMetadataCachePublisher with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,037] INFO [SocketServer listenerType=CONTROLLER, nodeId=1] Enabling request processing. (kafka.network.SocketServer)
[2025-07-06 09:04:28,037] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing FeaturesPublisher with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,037] INFO [ControllerServer id=1] Loaded new metadata Features(metadataVersion=3.8-IV0, finalizedFeatures={metadata.version=20}, finalizedFeaturesEpoch=393). (org.apache.kafka.metadata.publisher.FeaturesPublisher)
[2025-07-06 09:04:28,037] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing ControllerRegistrationsPublisher with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,037] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing ControllerRegistrationManager with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,038] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing DynamicConfigPublisher controller id=1 with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,038] INFO [ControllerRegistrationManager id=1 incarnation=DNidUPKcTBaMP1zkmouFAg] Found registration for dDkvVYmZRpiis_Rf1Fcrrg instead of our incarnation. (kafka.server.ControllerRegistrationManager)
[2025-07-06 09:04:28,038] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing DynamicClientQuotaPublisher controller id=1 with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,039] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing ScramPublisher controller id=1 with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,040] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing DelegationTokenPublisher controller id=1 with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,040] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing ControllerMetadataMetricsPublisher with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,041] INFO [MetadataLoader id=1] InitializeNewPublishers: initializing AclPublisher controller id=1 with a snapshot at offset 393 (org.apache.kafka.image.loader.MetadataLoader)
[2025-07-06 09:04:28,042] ERROR Unable to start acceptor for ListenerName(CONTROLLER) (kafka.network.DataPlaneAcceptor)
org.apache.kafka.common.KafkaException: Socket server failed to bind to 0.0.0.0:9093: Address already in use.
	at kafka.network.Acceptor.openServerSocket(SocketServer.scala:737)
	at kafka.network.Acceptor.liftedTree1$1(SocketServer.scala:633)
	at kafka.network.Acceptor.start(SocketServer.scala:628)
	at kafka.network.SocketServer.$anonfun$enableRequestProcessing$2(SocketServer.scala:224)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:774)
	at java.util.concurrent.CompletableFuture.uniWhenCompleteStage(CompletableFuture.java:792)
	at java.util.concurrent.CompletableFuture.whenComplete(CompletableFuture.java:2153)
	at kafka.network.SocketServer.chainAcceptorFuture$1(SocketServer.scala:217)
	at kafka.network.SocketServer.$anonfun$enableRequestProcessing$5(SocketServer.scala:231)
	at java.util.concurrent.ConcurrentHashMap$ValuesView.forEach(ConcurrentHashMap.java:4705)
	at kafka.network.SocketServer.enableRequestProcessing(SocketServer.scala:231)
	at kafka.server.ControllerServer.startup(ControllerServer.scala:434)
	at kafka.server.KafkaRaftServer.$anonfun$startup$1(KafkaRaftServer.scala:96)
	at kafka.server.KafkaRaftServer.$anonfun$startup$1$adapted(KafkaRaftServer.scala:96)
	at scala.Option.foreach(Option.scala:437)
	at kafka.server.KafkaRaftServer.startup(KafkaRaftServer.scala:96)
	at kafka.Kafka$.main(Kafka.scala:112)
	at kafka.Kafka.main(Kafka.scala)
Caused by: java.net.BindException: Address already in use
	at sun.nio.ch.Net.bind0(Native Method)
	at sun.nio.ch.Net.bind(Net.java:461)
	at sun.nio.ch.Net.bind(Net.java:453)
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:222)
	at sun.nio.ch.ServerSocketAdaptor.bind(ServerSocketAdaptor.java:85)
	at kafka.network.Acceptor.openServerSocket(SocketServer.scala:732)
	... 17 more
[2025-07-06 09:04:28,056] INFO [controller-1-to-controller-registration-channel-manager]: Starting (kafka.server.NodeToControllerRequestThread)
[2025-07-06 09:04:28,057] INFO [ControllerServer id=1] Waiting for all of the authorizer futures to be completed (kafka.server.ControllerServer)
[2025-07-06 09:04:28,057] INFO [ControllerRegistrationManager id=1 incarnation=DNidUPKcTBaMP1zkmouFAg] initialized channel manager. (kafka.server.ControllerRegistrationManager)
[2025-07-06 09:04:28,057] INFO [ControllerServer id=1] Finished waiting for all of the authorizer futures to be completed (kafka.server.ControllerServer)
[2025-07-06 09:04:28,057] INFO [controller-1-to-controller-registration-channel-manager]: Recorded new KRaft controller, from now on will use node localhost:9093 (id: 1 rack: null) (kafka.server.NodeToControllerRequestThread)
[2025-07-06 09:04:28,058] INFO [ControllerServer id=1] Waiting for all of the SocketServer Acceptors to be started (kafka.server.ControllerServer)
[2025-07-06 09:04:28,058] ERROR [ControllerServer id=1] Received a fatal error while waiting for all of the SocketServer Acceptors to be started (kafka.server.ControllerServer)
java.lang.RuntimeException: Unable to start acceptor for ListenerName(CONTROLLER)
	at kafka.network.Acceptor.liftedTree1$1(SocketServer.scala:648)
	at kafka.network.Acceptor.start(SocketServer.scala:628)
	at kafka.network.SocketServer.$anonfun$enableRequestProcessing$2(SocketServer.scala:224)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:774)
	at java.util.concurrent.CompletableFuture.uniWhenCompleteStage(CompletableFuture.java:792)
	at java.util.concurrent.CompletableFuture.whenComplete(CompletableFuture.java:2153)
	at kafka.network.SocketServer.chainAcceptorFuture$1(SocketServer.scala:217)
	at kafka.network.SocketServer.$anonfun$enableRequestProcessing$5(SocketServer.scala:231)
	at java.util.concurrent.ConcurrentHashMap$ValuesView.forEach(ConcurrentHashMap.java:4705)
	at kafka.network.SocketServer.enableRequestProcessing(SocketServer.scala:231)
	at kafka.server.ControllerServer.startup(ControllerServer.scala:434)
	at kafka.server.KafkaRaftServer.$anonfun$startup$1(KafkaRaftServer.scala:96)
	at kafka.server.KafkaRaftServer.$anonfun$startup$1$adapted(KafkaRaftServer.scala:96)
	at scala.Option.foreach(Option.scala:437)
	at kafka.server.KafkaRaftServer.startup(KafkaRaftServer.scala:96)
	at kafka.Kafka$.main(Kafka.scala:112)
	at kafka.Kafka.main(Kafka.scala)
Caused by: org.apache.kafka.common.KafkaException: Socket server failed to bind to 0.0.0.0:9093: Address already in use.
	at kafka.network.Acceptor.openServerSocket(SocketServer.scala:737)
	at kafka.network.Acceptor.liftedTree1$1(SocketServer.scala:633)
	... 16 more
Caused by: java.net.BindException: Address already in use
	at sun.nio.ch.Net.bind0(Native Method)
	at sun.nio.ch.Net.bind(Net.java:461)
	at sun.nio.ch.Net.bind(Net.java:453)
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:222)
	at sun.nio.ch.ServerSocketAdaptor.bind(ServerSocketAdaptor.java:85)
	at kafka.network.Acceptor.openServerSocket(SocketServer.scala:732)
	... 17 more
[2025-07-06 09:04:28,059] INFO [ControllerRegistrationManager id=1 incarnation=DNidUPKcTBaMP1zkmouFAg] sendControllerRegistration: attempting to send ControllerRegistrationRequestData(controllerId=1, incarnationId=DNidUPKcTBaMP1zkmouFAg, zkMigrationReady=false, listeners=[Listener(name='CONTROLLER', host='LAPTOP-H9435UC9.', port=9093, securityProtocol=0)], features=[Feature(name='metadata.version', minSupportedVersion=1, maxSupportedVersion=20)]) (kafka.server.ControllerRegistrationManager)
[2025-07-06 09:04:28,059] ERROR Encountered fatal fault: caught exception (org.apache.kafka.server.fault.ProcessTerminatingFaultHandler)
java.lang.RuntimeException: Received a fatal error while waiting for all of the SocketServer Acceptors to be started
	at org.apache.kafka.server.util.FutureUtils.waitWithLogging(FutureUtils.java:72)
	at kafka.server.ControllerServer.startup(ControllerServer.scala:459)
	at kafka.server.KafkaRaftServer.$anonfun$startup$1(KafkaRaftServer.scala:96)
	at kafka.server.KafkaRaftServer.$anonfun$startup$1$adapted(KafkaRaftServer.scala:96)
	at scala.Option.foreach(Option.scala:437)
	at kafka.server.KafkaRaftServer.startup(KafkaRaftServer.scala:96)
	at kafka.Kafka$.main(Kafka.scala:112)
	at kafka.Kafka.main(Kafka.scala)
Caused by: java.lang.RuntimeException: Unable to start acceptor for ListenerName(CONTROLLER)
	at kafka.network.Acceptor.liftedTree1$1(SocketServer.scala:648)
	at kafka.network.Acceptor.start(SocketServer.scala:628)
	at kafka.network.SocketServer.$anonfun$enableRequestProcessing$2(SocketServer.scala:224)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:774)
	at java.util.concurrent.CompletableFuture.uniWhenCompleteStage(CompletableFuture.java:792)
	at java.util.concurrent.CompletableFuture.whenComplete(CompletableFuture.java:2153)
	at kafka.network.SocketServer.chainAcceptorFuture$1(SocketServer.scala:217)
	at kafka.network.SocketServer.$anonfun$enableRequestProcessing$5(SocketServer.scala:231)
	at java.util.concurrent.ConcurrentHashMap$ValuesView.forEach(ConcurrentHashMap.java:4705)
	at kafka.network.SocketServer.enableRequestProcessing(SocketServer.scala:231)
	at kafka.server.ControllerServer.startup(ControllerServer.scala:434)
	... 6 more
Caused by: org.apache.kafka.common.KafkaException: Socket server failed to bind to 0.0.0.0:9093: Address already in use.
	at kafka.network.Acceptor.openServerSocket(SocketServer.scala:737)
	at kafka.network.Acceptor.liftedTree1$1(SocketServer.scala:633)
	... 16 more
Caused by: java.net.BindException: Address already in use
	at sun.nio.ch.Net.bind0(Native Method)
	at sun.nio.ch.Net.bind(Net.java:461)
	at sun.nio.ch.Net.bind(Net.java:453)
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:222)
	at sun.nio.ch.ServerSocketAdaptor.bind(ServerSocketAdaptor.java:85)
	at kafka.network.Acceptor.openServerSocket(SocketServer.scala:732)
	... 17 more
