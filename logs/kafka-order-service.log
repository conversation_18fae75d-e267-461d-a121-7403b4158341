{"@timestamp":"2025-07-06T10:21:23.614+08:00","level":"INFO","logger_name":"com.example.kafka.order.KafkaOrderServiceApplication","message":"Starting KafkaOrderServiceApplication v1.0.0 using Java 1.8.0_452 on LAPTOP-H9435UC9 with PID 34897 (/home/<USER>/code/ai/kafka_example/target/kafka-order-service-1.0.0.jar started by dianweita in /home/<USER>/code/ai/kafka_example)"}
{"@timestamp":"2025-07-06T10:21:23.620+08:00","level":"INFO","logger_name":"com.example.kafka.order.KafkaOrderServiceApplication","message":"The following profiles are active: dev"}
{"@timestamp":"2025-07-06T10:21:24.910+08:00","level":"INFO","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","message":"Tom<PERSON> initialized with port(s): 8080 (http)"}
{"@timestamp":"2025-07-06T10:21:24.934+08:00","level":"INFO","logger_name":"org.apache.catalina.core.StandardService","message":"Starting service [Tomcat]"}
{"@timestamp":"2025-07-06T10:21:24.934+08:00","level":"INFO","logger_name":"org.apache.catalina.core.StandardEngine","message":"Starting Servlet engine: [Apache Tomcat/9.0.55]"}
{"@timestamp":"2025-07-06T10:21:24.990+08:00","level":"INFO","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","message":"Initializing Spring embedded WebApplicationContext"}
{"@timestamp":"2025-07-06T10:21:24.991+08:00","level":"INFO","logger_name":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","message":"Root WebApplicationContext: initialization completed in 1315 ms"}
{"@timestamp":"2025-07-06T10:21:26.030+08:00","level":"INFO","logger_name":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","message":"Exposing 4 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-06T10:21:26.542+08:00","level":"INFO","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","message":"Tomcat started on port(s): 8080 (http) with context path ''"}
{"@timestamp":"2025-07-06T10:21:26.559+08:00","level":"INFO","logger_name":"com.example.kafka.order.KafkaOrderServiceApplication","message":"Started KafkaOrderServiceApplication in 3.842 seconds (JVM running for 4.198)"}
{"@timestamp":"2025-07-06T10:22:19.830+08:00","level":"INFO","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"@timestamp":"2025-07-06T10:22:19.830+08:00","level":"INFO","logger_name":"org.springframework.web.servlet.DispatcherServlet","message":"Initializing Servlet 'dispatcherServlet'"}
{"@timestamp":"2025-07-06T10:22:19.836+08:00","level":"INFO","logger_name":"org.springframework.web.servlet.DispatcherServlet","message":"Completed initialization in 5 ms"}
{"@timestamp":"2025-07-06T10:22:30.049+08:00","level":"INFO","logger_name":"com.example.kafka.order.controller.OrderController","message":"Received create order request for user: test-user-001"}
{"@timestamp":"2025-07-06T10:22:30.107+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.192+08:00","level":"INFO","logger_name":"com.example.kafka.order.controller.OrderController","message":"Order created successfully: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.263+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_CREATED event for order: ORD-1751768550050-4233A679 to partition: 0 with offset: 0"}
{"@timestamp":"2025-07-06T10:22:30.400+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order created event: ORD-1751768550050-4233A679 from topic: order-events, partition: 0, offset: 0"}
{"@timestamp":"2025-07-06T10:22:30.400+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Processing order creation: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.401+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Processing new order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.401+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Checking inventory for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.401+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Inventory check passed for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.401+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Calculating price for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.401+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Price calculation completed for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.401+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_STATUS_UPDATED event for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.406+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Successfully processed new order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.407+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Order creation processed: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.407+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Successfully processed order created event: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.422+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_STATUS_UPDATED event for order: ORD-1751768550050-4233A679 to partition: 0 with offset: 0"}
{"@timestamp":"2025-07-06T10:22:30.896+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order status update event: ORD-1751768550050-4233A679 with status: CONFIRMED from topic: order-status-updates, partition: 0, offset: 0"}
{"@timestamp":"2025-07-06T10:22:30.896+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Processing order status update: ORD-1751768550050-4233A679 to status: CONFIRMED"}
{"@timestamp":"2025-07-06T10:22:30.897+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Processing order status update: ORD-1751768550050-4233A679 to CONFIRMED"}
{"@timestamp":"2025-07-06T10:22:30.897+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Invalid status transition from CONFIRMED to CONFIRMED for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.898+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Order status update processed: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.898+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Successfully processed order status update event: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:23:09.246+08:00","level":"INFO","logger_name":"com.example.kafka.order.controller.OrderController","message":"Received batch create order request: 5 orders for user: batch-test-user"}
{"@timestamp":"2025-07-06T10:23:09.247+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751768589247-D1B84887"}
{"@timestamp":"2025-07-06T10:23:09.249+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751768589249-2061C22B"}
{"@timestamp":"2025-07-06T10:23:09.249+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751768589249-DC3F253D"}
{"@timestamp":"2025-07-06T10:23:09.250+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751768589250-4BE6E4E4"}
{"@timestamp":"2025-07-06T10:23:09.251+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751768589251-6A9A022A"}
{"@timestamp":"2025-07-06T10:23:09.253+08:00","level":"INFO","logger_name":"com.example.kafka.order.controller.OrderController","message":"成功创建 5 个批量订单"}
{"@timestamp":"2025-07-06T10:23:09.262+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_CREATED event for order: ORD-1751768589247-D1B84887 to partition: 0 with offset: 1"}
{"@timestamp":"2025-07-06T10:23:09.263+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_CREATED event for order: ORD-1751768589249-2061C22B to partition: 0 with offset: 2"}
{"@timestamp":"2025-07-06T10:23:09.263+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_CREATED event for order: ORD-1751768589249-DC3F253D to partition: 0 with offset: 3"}
{"@timestamp":"2025-07-06T10:23:09.263+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_CREATED event for order: ORD-1751768589250-4BE6E4E4 to partition: 0 with offset: 4"}
{"@timestamp":"2025-07-06T10:23:09.263+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_CREATED event for order: ORD-1751768589251-6A9A022A to partition: 0 with offset: 5"}
{"@timestamp":"2025-07-06T10:23:09.740+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order created event: ORD-1751768589247-D1B84887 from topic: order-events, partition: 0, offset: 1"}
{"@timestamp":"2025-07-06T10:23:09.741+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589247-D1B84887"}
{"@timestamp":"2025-07-06T10:23:09.744+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order created event: ORD-1751768589249-2061C22B from topic: order-events, partition: 0, offset: 2"}
{"@timestamp":"2025-07-06T10:23:09.744+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589249-2061C22B"}
{"@timestamp":"2025-07-06T10:23:09.747+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order created event: ORD-1751768589249-DC3F253D from topic: order-events, partition: 0, offset: 3"}
{"@timestamp":"2025-07-06T10:23:09.747+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589249-DC3F253D"}
{"@timestamp":"2025-07-06T10:23:09.749+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order created event: ORD-1751768589250-4BE6E4E4 from topic: order-events, partition: 0, offset: 4"}
{"@timestamp":"2025-07-06T10:23:09.749+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589250-4BE6E4E4"}
{"@timestamp":"2025-07-06T10:23:09.751+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order created event: ORD-1751768589251-6A9A022A from topic: order-events, partition: 0, offset: 5"}
{"@timestamp":"2025-07-06T10:23:09.751+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589251-6A9A022A"}
{"@timestamp":"2025-07-06T10:23:41.394+08:00","level":"INFO","logger_name":"com.example.kafka.order.controller.OrderController","message":"Received create order request for user: test-user-001"}
{"@timestamp":"2025-07-06T10:23:41.395+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.396+08:00","level":"INFO","logger_name":"com.example.kafka.order.controller.OrderController","message":"Order created successfully: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.408+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_CREATED event for order: ORD-1751768621394-D3EB1B70 to partition: 0 with offset: 6"}
{"@timestamp":"2025-07-06T10:23:41.835+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order created event: ORD-1751768621394-D3EB1B70 from topic: order-events, partition: 0, offset: 6"}
{"@timestamp":"2025-07-06T10:23:41.835+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Processing order creation: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.836+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Processing new order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.836+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Checking inventory for order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.837+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Inventory check passed for order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.837+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Calculating price for order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.837+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Price calculation completed for order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.837+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_STATUS_UPDATED event for order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.838+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Successfully processed new order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.838+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Order creation processed: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.839+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Successfully processed order created event: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.850+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_STATUS_UPDATED event for order: ORD-1751768621394-D3EB1B70 to partition: 0 with offset: 1"}
{"@timestamp":"2025-07-06T10:23:41.888+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order status update event: ORD-1751768621394-D3EB1B70 with status: CONFIRMED from topic: order-status-updates, partition: 0, offset: 1"}
{"@timestamp":"2025-07-06T10:23:41.889+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Processing order status update: ORD-1751768621394-D3EB1B70 to status: CONFIRMED"}
{"@timestamp":"2025-07-06T10:23:41.889+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Processing order status update: ORD-1751768621394-D3EB1B70 to CONFIRMED"}
{"@timestamp":"2025-07-06T10:23:41.889+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Invalid status transition from CONFIRMED to CONFIRMED for order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.889+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Order status update processed: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.890+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Successfully processed order status update event: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:59.544+08:00","level":"INFO","logger_name":"org.apache.coyote.http11.Http11Processor","message":"Error parsing HTTP request header\n Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.","stack_trace":"java.lang.IllegalArgumentException: Invalid character found in the request target [/api/v1/orders/ORD-1751768550050-4233A679/status?status=PAID&remarks=0xe60xb50x8b0xe80xaf0x950xe60x940xaf0xe40xbb0x98 ]. The valid characters are defined in RFC 7230 and RFC 3986\n\tat org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:494)\n\tat org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:269)\n\tat org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)\n\tat org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)\n\tat org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)\n\tat org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)\n\tat org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)\n\tat java.lang.Thread.run(Thread.java:750)\n"}
{"@timestamp":"2025-07-06T10:43:55.049+08:00","level":"INFO","logger_name":"com.example.kafka.order.KafkaOrderServiceApplication","message":"Starting KafkaOrderServiceApplication v1.0.0 using Java 1.8.0_452 on LAPTOP-H9435UC9 with PID 47752 (/home/<USER>/code/ai/kafka_example/target/kafka-order-service-1.0.0.jar started by dianweita in /home/<USER>/code/ai/kafka_example)"}
{"@timestamp":"2025-07-06T10:43:55.058+08:00","level":"INFO","logger_name":"com.example.kafka.order.KafkaOrderServiceApplication","message":"The following profiles are active: dev"}
{"@timestamp":"2025-07-06T10:43:56.365+08:00","level":"INFO","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","message":"Tomcat initialized with port(s): 8080 (http)"}
{"@timestamp":"2025-07-06T10:43:56.378+08:00","level":"INFO","logger_name":"org.apache.catalina.core.StandardService","message":"Starting service [Tomcat]"}
{"@timestamp":"2025-07-06T10:43:56.379+08:00","level":"INFO","logger_name":"org.apache.catalina.core.StandardEngine","message":"Starting Servlet engine: [Apache Tomcat/9.0.55]"}
{"@timestamp":"2025-07-06T10:43:56.423+08:00","level":"INFO","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","message":"Initializing Spring embedded WebApplicationContext"}
{"@timestamp":"2025-07-06T10:43:56.424+08:00","level":"INFO","logger_name":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","message":"Root WebApplicationContext: initialization completed in 1309 ms"}
{"@timestamp":"2025-07-06T10:43:57.287+08:00","level":"INFO","logger_name":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","message":"Exposing 4 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-06T10:43:57.809+08:00","level":"INFO","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","message":"Tomcat started on port(s): 8080 (http) with context path ''"}
{"@timestamp":"2025-07-06T10:43:57.828+08:00","level":"INFO","logger_name":"com.example.kafka.order.KafkaOrderServiceApplication","message":"Started KafkaOrderServiceApplication in 3.725 seconds (JVM running for 4.348)"}
{"@timestamp":"2025-07-06T10:44:07.144+08:00","level":"INFO","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"@timestamp":"2025-07-06T10:44:07.144+08:00","level":"INFO","logger_name":"org.springframework.web.servlet.DispatcherServlet","message":"Initializing Servlet 'dispatcherServlet'"}
{"@timestamp":"2025-07-06T10:44:07.146+08:00","level":"INFO","logger_name":"org.springframework.web.servlet.DispatcherServlet","message":"Completed initialization in 1 ms"}
{"@timestamp":"2025-07-06T13:57:03.648+08:00","level":"INFO","logger_name":"com.example.kafka.order.controller.OrderController","message":"Received batch create order request: 5 orders for user: timer-test"}
{"@timestamp":"2025-07-06T13:57:03.666+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751781423651-99449B75"}
{"@timestamp":"2025-07-06T13:58:03.799+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Exception occurred while sending ORDER_CREATED event for order: ORD-1751781423651-99449B75","stack_trace":"org.springframework.kafka.KafkaException: Send failed; nested exception is org.apache.kafka.common.errors.TimeoutException: Topic order-events not present in metadata after 60000 ms.\n\tat org.springframework.kafka.core.KafkaTemplate.doSend(KafkaTemplate.java:619)\n\tat org.springframework.kafka.core.KafkaTemplate.send(KafkaTemplate.java:414)\n\tat com.example.kafka.order.service.OrderProducerService.sendOrderEvent(OrderProducerService.java:102)\n\tat com.example.kafka.order.service.OrderProducerService.sendOrderCreatedEvent(OrderProducerService.java:74)\n\tat com.example.kafka.order.controller.OrderController.createBatchOrders(OrderController.java:198)\n\tat com.example.kafka.order.controller.OrderController$$FastClassBySpringCGLIB$$82a3091.invoke(<generated>)\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)\n\tat org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)\n\tat org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:698)\n\tat com.example.kafka.order.controller.OrderController$$EnhancerBySpringCGLIB$$9c359995.createBatchOrders(<generated>)\n\tat sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\n\tat sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\n\tat sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tat java.lang.reflect.Method.invoke(Method.java:498)\n\tat org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)\n\tat org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)\n\tat org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)\n\tat org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)\n\tat org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)\n\tat org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)\n\tat org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:681)\n\tat org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:764)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)\n\tat org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)\n\tat org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)\n\tat org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)\n\tat org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)\n\tat org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)\n\tat org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)\n\tat org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)\n\tat org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)\n\tat org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)\n\tat org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)\n\tat org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)\n\tat org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)\n\tat java.lang.Thread.run(Thread.java:750)\nCaused by: org.apache.kafka.common.errors.TimeoutException: Topic order-events not present in metadata after 60000 ms.\n"}
{"@timestamp":"2025-07-06T13:58:03.805+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751781483804-2EA2E247"}
{"@timestamp":"2025-07-06T13:59:03.806+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Exception occurred while sending ORDER_CREATED event for order: ORD-1751781483804-2EA2E247","stack_trace":"org.springframework.kafka.KafkaException: Send failed; nested exception is org.apache.kafka.common.errors.TimeoutException: Topic order-events not present in metadata after 60000 ms.\n\tat org.springframework.kafka.core.KafkaTemplate.doSend(KafkaTemplate.java:619)\n\tat org.springframework.kafka.core.KafkaTemplate.send(KafkaTemplate.java:414)\n\tat com.example.kafka.order.service.OrderProducerService.sendOrderEvent(OrderProducerService.java:102)\n\tat com.example.kafka.order.service.OrderProducerService.sendOrderCreatedEvent(OrderProducerService.java:74)\n\tat com.example.kafka.order.controller.OrderController.createBatchOrders(OrderController.java:198)\n\tat com.example.kafka.order.controller.OrderController$$FastClassBySpringCGLIB$$82a3091.invoke(<generated>)\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)\n\tat org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)\n\tat org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:698)\n\tat com.example.kafka.order.controller.OrderController$$EnhancerBySpringCGLIB$$9c359995.createBatchOrders(<generated>)\n\tat sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\n\tat sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\n\tat sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tat java.lang.reflect.Method.invoke(Method.java:498)\n\tat org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)\n\tat org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)\n\tat org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)\n\tat org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)\n\tat org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)\n\tat org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)\n\tat org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:681)\n\tat org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:764)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)\n\tat org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)\n\tat org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)\n\tat org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)\n\tat org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)\n\tat org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)\n\tat org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)\n\tat org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)\n\tat org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)\n\tat org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)\n\tat org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)\n\tat org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)\n\tat org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)\n\tat java.lang.Thread.run(Thread.java:750)\nCaused by: org.apache.kafka.common.errors.TimeoutException: Topic order-events not present in metadata after 60000 ms.\n"}
{"@timestamp":"2025-07-06T13:59:03.810+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751781543810-74424AC9"}
{"@timestamp":"2025-07-06T14:00:03.812+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Exception occurred while sending ORDER_CREATED event for order: ORD-1751781543810-74424AC9","stack_trace":"org.springframework.kafka.KafkaException: Send failed; nested exception is org.apache.kafka.common.errors.TimeoutException: Topic order-events not present in metadata after 60000 ms.\n\tat org.springframework.kafka.core.KafkaTemplate.doSend(KafkaTemplate.java:619)\n\tat org.springframework.kafka.core.KafkaTemplate.send(KafkaTemplate.java:414)\n\tat com.example.kafka.order.service.OrderProducerService.sendOrderEvent(OrderProducerService.java:102)\n\tat com.example.kafka.order.service.OrderProducerService.sendOrderCreatedEvent(OrderProducerService.java:74)\n\tat com.example.kafka.order.controller.OrderController.createBatchOrders(OrderController.java:198)\n\tat com.example.kafka.order.controller.OrderController$$FastClassBySpringCGLIB$$82a3091.invoke(<generated>)\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)\n\tat org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)\n\tat org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:698)\n\tat com.example.kafka.order.controller.OrderController$$EnhancerBySpringCGLIB$$9c359995.createBatchOrders(<generated>)\n\tat sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\n\tat sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\n\tat sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tat java.lang.reflect.Method.invoke(Method.java:498)\n\tat org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)\n\tat org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)\n\tat org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)\n\tat org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)\n\tat org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)\n\tat org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)\n\tat org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:681)\n\tat org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:764)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)\n\tat org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)\n\tat org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)\n\tat org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)\n\tat org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)\n\tat org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)\n\tat org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)\n\tat org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)\n\tat org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)\n\tat org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)\n\tat org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)\n\tat org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)\n\tat org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)\n\tat java.lang.Thread.run(Thread.java:750)\nCaused by: org.apache.kafka.common.errors.TimeoutException: Topic order-events not present in metadata after 60000 ms.\n"}
{"@timestamp":"2025-07-06T14:00:03.817+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751781603817-7E3F2BB5"}
{"@timestamp":"2025-07-06T14:01:03.820+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Exception occurred while sending ORDER_CREATED event for order: ORD-1751781603817-7E3F2BB5","stack_trace":"org.springframework.kafka.KafkaException: Send failed; nested exception is org.apache.kafka.common.errors.TimeoutException: Topic order-events not present in metadata after 60000 ms.\n\tat org.springframework.kafka.core.KafkaTemplate.doSend(KafkaTemplate.java:619)\n\tat org.springframework.kafka.core.KafkaTemplate.send(KafkaTemplate.java:414)\n\tat com.example.kafka.order.service.OrderProducerService.sendOrderEvent(OrderProducerService.java:102)\n\tat com.example.kafka.order.service.OrderProducerService.sendOrderCreatedEvent(OrderProducerService.java:74)\n\tat com.example.kafka.order.controller.OrderController.createBatchOrders(OrderController.java:198)\n\tat com.example.kafka.order.controller.OrderController$$FastClassBySpringCGLIB$$82a3091.invoke(<generated>)\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)\n\tat org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)\n\tat org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:698)\n\tat com.example.kafka.order.controller.OrderController$$EnhancerBySpringCGLIB$$9c359995.createBatchOrders(<generated>)\n\tat sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\n\tat sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\n\tat sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tat java.lang.reflect.Method.invoke(Method.java:498)\n\tat org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)\n\tat org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)\n\tat org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)\n\tat org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)\n\tat org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)\n\tat org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)\n\tat org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:681)\n\tat org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:764)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)\n\tat org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)\n\tat org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)\n\tat org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)\n\tat org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)\n\tat org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)\n\tat org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)\n\tat org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)\n\tat org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)\n\tat org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)\n\tat org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)\n\tat org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)\n\tat org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)\n\tat java.lang.Thread.run(Thread.java:750)\nCaused by: org.apache.kafka.common.errors.TimeoutException: Topic order-events not present in metadata after 60000 ms.\n"}
{"@timestamp":"2025-07-06T14:01:03.822+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751781663822-49479E87"}
{"@timestamp":"2025-07-06T14:02:03.824+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Exception occurred while sending ORDER_CREATED event for order: ORD-1751781663822-49479E87","stack_trace":"org.springframework.kafka.KafkaException: Send failed; nested exception is org.apache.kafka.common.errors.TimeoutException: Topic order-events not present in metadata after 60000 ms.\n\tat org.springframework.kafka.core.KafkaTemplate.doSend(KafkaTemplate.java:619)\n\tat org.springframework.kafka.core.KafkaTemplate.send(KafkaTemplate.java:414)\n\tat com.example.kafka.order.service.OrderProducerService.sendOrderEvent(OrderProducerService.java:102)\n\tat com.example.kafka.order.service.OrderProducerService.sendOrderCreatedEvent(OrderProducerService.java:74)\n\tat com.example.kafka.order.controller.OrderController.createBatchOrders(OrderController.java:198)\n\tat com.example.kafka.order.controller.OrderController$$FastClassBySpringCGLIB$$82a3091.invoke(<generated>)\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)\n\tat org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)\n\tat org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:698)\n\tat com.example.kafka.order.controller.OrderController$$EnhancerBySpringCGLIB$$9c359995.createBatchOrders(<generated>)\n\tat sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\n\tat sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\n\tat sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tat java.lang.reflect.Method.invoke(Method.java:498)\n\tat org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)\n\tat org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)\n\tat org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)\n\tat org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)\n\tat org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)\n\tat org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)\n\tat org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:681)\n\tat org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:764)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\n\tat org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)\n\tat org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)\n\tat org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)\n\tat org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)\n\tat org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)\n\tat org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)\n\tat org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)\n\tat org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)\n\tat org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)\n\tat org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)\n\tat org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)\n\tat org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)\n\tat org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)\n\tat java.lang.Thread.run(Thread.java:750)\nCaused by: org.apache.kafka.common.errors.TimeoutException: Topic order-events not present in metadata after 60000 ms.\n"}
{"@timestamp":"2025-07-06T14:02:03.824+08:00","level":"INFO","logger_name":"com.example.kafka.order.controller.OrderController","message":"成功创建 5 个批量订单"}
