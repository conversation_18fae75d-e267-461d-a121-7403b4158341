{"@timestamp":"2025-07-06T10:21:23.614+08:00","level":"INFO","logger_name":"com.example.kafka.order.KafkaOrderServiceApplication","message":"Starting KafkaOrderServiceApplication v1.0.0 using Java 1.8.0_452 on LAPTOP-H9435UC9 with PID 34897 (/home/<USER>/code/ai/kafka_example/target/kafka-order-service-1.0.0.jar started by dianweita in /home/<USER>/code/ai/kafka_example)"}
{"@timestamp":"2025-07-06T10:21:23.620+08:00","level":"INFO","logger_name":"com.example.kafka.order.KafkaOrderServiceApplication","message":"The following profiles are active: dev"}
{"@timestamp":"2025-07-06T10:21:24.910+08:00","level":"INFO","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","message":"Tom<PERSON> initialized with port(s): 8080 (http)"}
{"@timestamp":"2025-07-06T10:21:24.934+08:00","level":"INFO","logger_name":"org.apache.catalina.core.StandardService","message":"Starting service [Tomcat]"}
{"@timestamp":"2025-07-06T10:21:24.934+08:00","level":"INFO","logger_name":"org.apache.catalina.core.StandardEngine","message":"Starting Servlet engine: [Apache Tomcat/9.0.55]"}
{"@timestamp":"2025-07-06T10:21:24.990+08:00","level":"INFO","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","message":"Initializing Spring embedded WebApplicationContext"}
{"@timestamp":"2025-07-06T10:21:24.991+08:00","level":"INFO","logger_name":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","message":"Root WebApplicationContext: initialization completed in 1315 ms"}
{"@timestamp":"2025-07-06T10:21:26.030+08:00","level":"INFO","logger_name":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","message":"Exposing 4 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-06T10:21:26.542+08:00","level":"INFO","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","message":"Tomcat started on port(s): 8080 (http) with context path ''"}
{"@timestamp":"2025-07-06T10:21:26.559+08:00","level":"INFO","logger_name":"com.example.kafka.order.KafkaOrderServiceApplication","message":"Started KafkaOrderServiceApplication in 3.842 seconds (JVM running for 4.198)"}
{"@timestamp":"2025-07-06T10:22:19.830+08:00","level":"INFO","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"@timestamp":"2025-07-06T10:22:19.830+08:00","level":"INFO","logger_name":"org.springframework.web.servlet.DispatcherServlet","message":"Initializing Servlet 'dispatcherServlet'"}
{"@timestamp":"2025-07-06T10:22:19.836+08:00","level":"INFO","logger_name":"org.springframework.web.servlet.DispatcherServlet","message":"Completed initialization in 5 ms"}
{"@timestamp":"2025-07-06T10:22:30.049+08:00","level":"INFO","logger_name":"com.example.kafka.order.controller.OrderController","message":"Received create order request for user: test-user-001"}
{"@timestamp":"2025-07-06T10:22:30.107+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.192+08:00","level":"INFO","logger_name":"com.example.kafka.order.controller.OrderController","message":"Order created successfully: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.263+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_CREATED event for order: ORD-1751768550050-4233A679 to partition: 0 with offset: 0"}
{"@timestamp":"2025-07-06T10:22:30.400+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order created event: ORD-1751768550050-4233A679 from topic: order-events, partition: 0, offset: 0"}
{"@timestamp":"2025-07-06T10:22:30.400+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Processing order creation: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.401+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Processing new order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.401+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Checking inventory for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.401+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Inventory check passed for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.401+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Calculating price for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.401+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Price calculation completed for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.401+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_STATUS_UPDATED event for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.406+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Successfully processed new order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.407+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Order creation processed: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.407+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Successfully processed order created event: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.422+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_STATUS_UPDATED event for order: ORD-1751768550050-4233A679 to partition: 0 with offset: 0"}
{"@timestamp":"2025-07-06T10:22:30.896+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order status update event: ORD-1751768550050-4233A679 with status: CONFIRMED from topic: order-status-updates, partition: 0, offset: 0"}
{"@timestamp":"2025-07-06T10:22:30.896+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Processing order status update: ORD-1751768550050-4233A679 to status: CONFIRMED"}
{"@timestamp":"2025-07-06T10:22:30.897+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Processing order status update: ORD-1751768550050-4233A679 to CONFIRMED"}
{"@timestamp":"2025-07-06T10:22:30.897+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Invalid status transition from CONFIRMED to CONFIRMED for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.898+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Order status update processed: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:22:30.898+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Successfully processed order status update event: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:23:09.246+08:00","level":"INFO","logger_name":"com.example.kafka.order.controller.OrderController","message":"Received batch create order request: 5 orders for user: batch-test-user"}
{"@timestamp":"2025-07-06T10:23:09.247+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751768589247-D1B84887"}
{"@timestamp":"2025-07-06T10:23:09.249+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751768589249-2061C22B"}
{"@timestamp":"2025-07-06T10:23:09.249+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751768589249-DC3F253D"}
{"@timestamp":"2025-07-06T10:23:09.250+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751768589250-4BE6E4E4"}
{"@timestamp":"2025-07-06T10:23:09.251+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751768589251-6A9A022A"}
{"@timestamp":"2025-07-06T10:23:09.253+08:00","level":"INFO","logger_name":"com.example.kafka.order.controller.OrderController","message":"成功创建 5 个批量订单"}
{"@timestamp":"2025-07-06T10:23:09.262+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_CREATED event for order: ORD-1751768589247-D1B84887 to partition: 0 with offset: 1"}
{"@timestamp":"2025-07-06T10:23:09.263+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_CREATED event for order: ORD-1751768589249-2061C22B to partition: 0 with offset: 2"}
{"@timestamp":"2025-07-06T10:23:09.263+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_CREATED event for order: ORD-1751768589249-DC3F253D to partition: 0 with offset: 3"}
{"@timestamp":"2025-07-06T10:23:09.263+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_CREATED event for order: ORD-1751768589250-4BE6E4E4 to partition: 0 with offset: 4"}
{"@timestamp":"2025-07-06T10:23:09.263+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_CREATED event for order: ORD-1751768589251-6A9A022A to partition: 0 with offset: 5"}
{"@timestamp":"2025-07-06T10:23:09.740+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order created event: ORD-1751768589247-D1B84887 from topic: order-events, partition: 0, offset: 1"}
{"@timestamp":"2025-07-06T10:23:09.741+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589247-D1B84887"}
{"@timestamp":"2025-07-06T10:23:09.744+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order created event: ORD-1751768589249-2061C22B from topic: order-events, partition: 0, offset: 2"}
{"@timestamp":"2025-07-06T10:23:09.744+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589249-2061C22B"}
{"@timestamp":"2025-07-06T10:23:09.747+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order created event: ORD-1751768589249-DC3F253D from topic: order-events, partition: 0, offset: 3"}
{"@timestamp":"2025-07-06T10:23:09.747+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589249-DC3F253D"}
{"@timestamp":"2025-07-06T10:23:09.749+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order created event: ORD-1751768589250-4BE6E4E4 from topic: order-events, partition: 0, offset: 4"}
{"@timestamp":"2025-07-06T10:23:09.749+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589250-4BE6E4E4"}
{"@timestamp":"2025-07-06T10:23:09.751+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order created event: ORD-1751768589251-6A9A022A from topic: order-events, partition: 0, offset: 5"}
{"@timestamp":"2025-07-06T10:23:09.751+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589251-6A9A022A"}
{"@timestamp":"2025-07-06T10:23:41.394+08:00","level":"INFO","logger_name":"com.example.kafka.order.controller.OrderController","message":"Received create order request for user: test-user-001"}
{"@timestamp":"2025-07-06T10:23:41.395+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_CREATED event for order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.396+08:00","level":"INFO","logger_name":"com.example.kafka.order.controller.OrderController","message":"Order created successfully: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.408+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_CREATED event for order: ORD-1751768621394-D3EB1B70 to partition: 0 with offset: 6"}
{"@timestamp":"2025-07-06T10:23:41.835+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order created event: ORD-1751768621394-D3EB1B70 from topic: order-events, partition: 0, offset: 6"}
{"@timestamp":"2025-07-06T10:23:41.835+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Processing order creation: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.836+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Processing new order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.836+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Checking inventory for order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.837+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Inventory check passed for order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.837+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Calculating price for order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.837+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Price calculation completed for order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.837+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Sending ORDER_STATUS_UPDATED event for order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.838+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Successfully processed new order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.838+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Order creation processed: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.839+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Successfully processed order created event: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.850+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProducerService","message":"Successfully sent ORDER_STATUS_UPDATED event for order: ORD-1751768621394-D3EB1B70 to partition: 0 with offset: 1"}
{"@timestamp":"2025-07-06T10:23:41.888+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Received order status update event: ORD-1751768621394-D3EB1B70 with status: CONFIRMED from topic: order-status-updates, partition: 0, offset: 1"}
{"@timestamp":"2025-07-06T10:23:41.889+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Processing order status update: ORD-1751768621394-D3EB1B70 to status: CONFIRMED"}
{"@timestamp":"2025-07-06T10:23:41.889+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Processing order status update: ORD-1751768621394-D3EB1B70 to CONFIRMED"}
{"@timestamp":"2025-07-06T10:23:41.889+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Invalid status transition from CONFIRMED to CONFIRMED for order: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.889+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Order status update processed: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:41.890+08:00","level":"INFO","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Successfully processed order status update event: ORD-1751768621394-D3EB1B70"}
{"@timestamp":"2025-07-06T10:23:59.544+08:00","level":"INFO","logger_name":"org.apache.coyote.http11.Http11Processor","message":"Error parsing HTTP request header\n Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.","stack_trace":"java.lang.IllegalArgumentException: Invalid character found in the request target [/api/v1/orders/ORD-1751768550050-4233A679/status?status=PAID&remarks=0xe60xb50x8b0xe80xaf0x950xe60x940xaf0xe40xbb0x98 ]. The valid characters are defined in RFC 7230 and RFC 3986\n\tat org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:494)\n\tat org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:269)\n\tat org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)\n\tat org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)\n\tat org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1722)\n\tat org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)\n\tat org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)\n\tat java.lang.Thread.run(Thread.java:750)\n"}
