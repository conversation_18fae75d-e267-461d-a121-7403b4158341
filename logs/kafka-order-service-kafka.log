{"@timestamp":"2025-07-06T10:21:26.852+08:00","level":"WARN","logger_name":"org.apache.kafka.clients.NetworkClient","message":"[Consumer clientId=consumer-order-service-group-dlq-7, groupId=order-service-group-dlq] Error while fetching metadata with correlation id 2 : {order-dlq=UNKNOWN_TOPIC_OR_PARTITION}"}
{"@timestamp":"2025-07-06T10:21:26.852+08:00","level":"WARN","logger_name":"org.apache.kafka.clients.NetworkClient","message":"[Consumer clientId=consumer-order-service-group-5, groupId=order-service-group] Error while fetching metadata with correlation id 2 : {order-status-updates=UNKNOWN_TOPIC_OR_PARTITION}"}
{"@timestamp":"2025-07-06T10:21:26.852+08:00","level":"WARN","logger_name":"org.apache.kafka.clients.NetworkClient","message":"[Consumer clientId=consumer-order-service-group-dlq-8, groupId=order-service-group-dlq] Error while fetching metadata with correlation id 2 : {order-dlq=UNKNOWN_TOPIC_OR_PARTITION}"}
{"@timestamp":"2025-07-06T10:21:26.852+08:00","level":"WARN","logger_name":"org.apache.kafka.clients.NetworkClient","message":"[Consumer clientId=consumer-order-service-group-2, groupId=order-service-group] Error while fetching metadata with correlation id 2 : {order-events=UNKNOWN_TOPIC_OR_PARTITION}"}
{"@timestamp":"2025-07-06T10:21:26.852+08:00","level":"WARN","logger_name":"org.apache.kafka.clients.NetworkClient","message":"[Consumer clientId=consumer-order-service-group-1, groupId=order-service-group] Error while fetching metadata with correlation id 2 : {order-events=UNKNOWN_TOPIC_OR_PARTITION}"}
{"@timestamp":"2025-07-06T10:21:26.852+08:00","level":"WARN","logger_name":"org.apache.kafka.clients.NetworkClient","message":"[Consumer clientId=consumer-order-service-group-6, groupId=order-service-group] Error while fetching metadata with correlation id 2 : {order-status-updates=UNKNOWN_TOPIC_OR_PARTITION}"}
{"@timestamp":"2025-07-06T10:21:26.852+08:00","level":"WARN","logger_name":"org.apache.kafka.clients.NetworkClient","message":"[Consumer clientId=consumer-order-service-group-4, groupId=order-service-group] Error while fetching metadata with correlation id 2 : {order-status-updates=UNKNOWN_TOPIC_OR_PARTITION}"}
{"@timestamp":"2025-07-06T10:21:26.853+08:00","level":"WARN","logger_name":"org.apache.kafka.clients.NetworkClient","message":"[Consumer clientId=consumer-order-service-group-3, groupId=order-service-group] Error while fetching metadata with correlation id 2 : {order-events=UNKNOWN_TOPIC_OR_PARTITION}"}
{"@timestamp":"2025-07-06T10:21:26.852+08:00","level":"WARN","logger_name":"org.apache.kafka.clients.NetworkClient","message":"[Consumer clientId=consumer-order-service-group-dlq-9, groupId=order-service-group-dlq] Error while fetching metadata with correlation id 2 : {order-dlq=UNKNOWN_TOPIC_OR_PARTITION}"}
