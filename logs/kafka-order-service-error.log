{"@timestamp":"2025-07-06T10:22:30.897+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Invalid status transition from CONFIRMED to CONFIRMED for order: ORD-1751768550050-4233A679"}
{"@timestamp":"2025-07-06T10:23:09.741+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589247-D1B84887"}
{"@timestamp":"2025-07-06T10:23:09.744+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589249-2061C22B"}
{"@timestamp":"2025-07-06T10:23:09.747+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589249-DC3F253D"}
{"@timestamp":"2025-07-06T10:23:09.749+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589250-4BE6E4E4"}
{"@timestamp":"2025-07-06T10:23:09.751+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderConsumerService","message":"Invalid order data: ORD-1751768589251-6A9A022A"}
{"@timestamp":"2025-07-06T10:23:41.889+08:00","level":"ERROR","logger_name":"com.example.kafka.order.service.OrderProcessingService","message":"Invalid status transition from CONFIRMED to CONFIRMED for order: ORD-1751768621394-D3EB1B70"}
